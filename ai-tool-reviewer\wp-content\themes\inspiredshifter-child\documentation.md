# InspiredShifter Child Theme Documentation

## Overview
This documentation provides detailed instructions for setting up and customizing the InspiredShifter Child Theme for WordPress. The theme is designed for showcasing AI tools with affiliate links and is built on top of the Astra theme.

## Installation

### Prerequisites
1. WordPress installation (5.0 or higher)
2. Astra theme (must be installed, but doesn't need to be active)
3. Advanced Custom Fields (ACF) Pro plugin (recommended) or the free version
4. Contact Form 7 plugin (for contact forms)

### Installation Steps
1. Download the `inspiredshifter-child.zip` file
2. Log in to your WordPress admin dashboard
3. Go to Appearance > Themes > Add New > Upload Theme
4. Choose the `inspiredshifter-child.zip` file and click "Install Now"
5. After installation, click "Activate"

## Theme Structure
The theme includes the following key files:

- `style.css` - Main stylesheet with theme information and styling
- `functions.php` - Core functionality including custom post types, taxonomies, and ACF fields
- `front-page.php` - Homepage template
- `single-ai_tool.php` - Single AI tool template
- `taxonomy-ai_category.php` - AI category archive template
- `template-landing.php` - Email capture landing page template
- `template-about.php` - About page template
- `template-contact.php` - Contact page template
- `single.php` - Blog post template

## Initial Setup

### 1. Install Required Plugins
After activating the theme, install and activate the following plugins:

- **Advanced Custom Fields (ACF)** - Required for custom fields
- **Contact Form 7** - Required for contact forms

### 2. Set Up ACF Fields
The theme automatically registers ACF fields when activated, but you need to ensure ACF is active first. If the fields don't appear:

1. Deactivate the child theme
2. Make sure ACF is activated
3. Reactivate the child theme

You should now see custom fields when editing:
- AI Tools (custom post type)
- AI Categories (taxonomy)

### 3. Create Navigation Menu
1. Go to Appearance > Menus
2. Create a new menu (e.g., "Main Menu")
3. Add the following menu items:
   - **AI Category** (as a parent item)
     - Add subcategories as child items (AI Chat Models, AI Image Generators, etc.)
   - **AI Blogs** (linking to your blog page)
   - **About Us** (linking to your about page)
   - **Contact Us** (linking to your contact page)
4. Add a "Sign Up" button (as a custom link)
5. Assign the menu to the "Primary Menu" location

### 4. Create Essential Pages
Create the following pages and assign the appropriate templates:

1. **Homepage**:
   - Create a new page (e.g., "Home")
   - Set it as the static front page in Settings > Reading
   - The front-page.php template will be used automatically

2. **About Us Page**:
   - Create a new page titled "About Us"
   - Set the template to "About Us Page" in the Page Attributes section

3. **Contact Page**:
   - Create a new page titled "Contact Us"
   - Set the template to "Contact Page" in the Page Attributes section

4. **Landing Page**:
   - Create a new page for email capture
   - Set the template to "Email Capture Landing Page"

5. **Blog Page**:
   - Create a new page titled "AI Blogs"
   - Set it as the Posts page in Settings > Reading

### 5. Set Up Contact Forms
1. Go to Contact > Contact Forms
2. Create two forms:
   - Contact page form
   - Email capture form
3. Copy the shortcodes and add them to the respective pages or update the ACF fields

## Adding Content

### Creating AI Categories
1. Go to AI Tools > AI Categories
2. Add a new category (e.g., "AI Chat Models")
3. Fill in the following fields:
   - Name
   - Slug (e.g., "ai-chat-models")
   - Description
   - Category Color (using the color picker)
   - Category Image (upload an image)
4. Repeat for all required categories

### Adding AI Tools
1. Go to AI Tools > Add New
2. Fill in the following information:
   - Title (tool name)
   - Content (detailed description)
   - Featured Image (tool logo)
   - Tool Details (ACF fields):
     - Tool Name
     - Short Description
     - Long Description
     - Pricing Type (Free, Freemium, Paid)
     - Starting Price (if applicable)
     - Pricing Plans
     - Features
     - Pros
     - Cons
     - Screenshots
     - Rating
     - Popular Tool (checkbox)
     - Featured Tool (checkbox)
     - Affiliate Link
3. Assign the tool to appropriate AI Categories
4. Assign relevant AI Features
5. Select the Pricing Model
6. Publish the tool

### Creating Blog Posts
1. Go to Posts > Add New
2. Create blog posts about AI tools, tutorials, reviews, etc.
3. Use the [affiliate_link] shortcode to add affiliate links in your content:
   ```
   [affiliate_link url="https://example.com/affiliate-link" text="Get Tool Name" class="button"]
   ```

## Customization

### Theme Colors
The theme uses CSS variables for colors, which can be modified in the `style.css` file:

```css
:root {
    --primary-blue: #3a5cc9;
    --dark-blue: #2d49a0;
    --light-blue: #99CCFF;
    --text-gray: #4a5568;
    --title-gray: #2d3748;
    --background-gray: #f5f7fa;
    --border-gray: #e0e0e0;
    --orange-accent: #ff7f45;
    --teal-accent: #32CDB8;
    --purple-accent: #7e5bef;
}
```

### Typography
The theme uses Google Fonts:
- Montserrat (for headings)
- Open Sans (for body text)

You can modify font settings in the `style.css` file.

### Header Customization
1. Go to Appearance > Customize
2. Navigate to Header Builder
3. Customize the header layout:
   - Logo position
   - Menu position
   - Add the Sign Up button
   - Adjust spacing and alignment

### Homepage Sections
The homepage is divided into sections that can be customized:

1. **Hero Section**:
   - Update the background image
   - Modify heading and subheading text
   - Change CTA button text and links

2. **Featured Tools Section**:
   - Tools marked as "Featured" will appear here
   - Customize the section title and description

3. **Resources Section**:
   - Modify the content and links

4. **Categories Section**:
   - Featured categories will appear here
   - Customize images and descriptions

## Advanced Customization

### Adding Custom CSS
1. Go to Appearance > Customize
2. Click on "Additional CSS"
3. Add your custom CSS rules

### Modifying Templates
If you need to make significant changes to the templates:

1. Create a child theme of this child theme or modify the existing files
2. Edit the PHP template files as needed
3. Make sure to test thoroughly after changes

### Adding New Templates
To create additional page templates:

1. Create a new PHP file in the theme directory
2. Add the template header comment:
   ```php
   <?php
   /**
    * Template Name: Your Template Name
    *
    * @package InspiredShifter Child
    */
   ```
3. Build your template content
4. The template will appear in the Page Attributes template dropdown

## Troubleshooting

### ACF Fields Not Appearing
1. Make sure ACF plugin is activated
2. Deactivate and reactivate the child theme
3. Check if the fields are registered in the ACF admin interface

### Styling Issues
1. Clear your browser cache
2. Check for conflicts with other plugins
3. Inspect elements using browser developer tools to identify CSS issues

### Menu Not Displaying Correctly
1. Verify the menu is assigned to the "Primary Menu" location
2. Check if all menu items are published
3. Ensure the menu structure matches the expected hierarchy

## Support
For additional support or questions, please contact <NAME_EMAIL>