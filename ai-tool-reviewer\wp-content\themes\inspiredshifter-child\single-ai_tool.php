<?php
/**
 * The template for displaying single AI Tool posts
 *
 * @package InspiredShifter Child
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">

        <?php
        while ( have_posts() ) :
            the_post();
            ?>

            <article id="post-<?php the_ID(); ?>" <?php post_class('ai-tool-single'); ?>>
                <div class="tool-hero">
                    <div class="container">
                        <div class="tool-hero-content">
                            <?php if ( has_post_thumbnail() ) : ?>
                                <div class="tool-logo">
                                    <?php the_post_thumbnail( 'thumbnail' ); ?>
                                </div>
                            <?php else : ?>
                                <div class="tool-logo-placeholder" style="background-color: #3a5cc9;">
                                    <?php echo substr(get_the_title(), 0, 2); ?>
                                </div>
                            <?php endif; ?>

                            <div class="tool-header-info">
                                <h1 class="tool-title"><?php the_title(); ?></h1>
                                
                                <?php if ( function_exists('get_field') && get_field('short_description') ) : ?>
                                    <p class="tool-short-description"><?php echo get_field('short_description'); ?></p>
                                <?php endif; ?>
                                
                                <div class="tool-meta">
                                    <?php if ( function_exists('get_field') ) : ?>
                                        <?php if ( get_field('pricing_type') ) : ?>
                                            <div class="tool-pricing-badge">
                                                <?php 
                                                $pricing_type = get_field('pricing_type');
                                                if ( $pricing_type == 'free' ) {
                                                    echo '<span class="pricing-free">Free</span>';
                                                } elseif ( $pricing_type == 'freemium' ) {
                                                    echo '<span class="pricing-freemium">Freemium</span>';
                                                } elseif ( $pricing_type == 'paid' ) {
                                                    echo '<span class="pricing-paid">Paid</span>';
                                                }
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ( get_field('rating') ) : ?>
                                            <div class="tool-rating">
                                                <span class="rating-value"><?php echo get_field('rating'); ?>/5</span>
                                                <div class="rating-stars">
                                                    <?php
                                                    $rating = get_field('rating');
                                                    $full_stars = floor($rating);
                                                    $half_star = ($rating - $full_stars) >= 0.5;
                                                    $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);
                                                    
                                                    // Full stars
                                                    for ($i = 0; $i < $full_stars; $i++) {
                                                        echo '<span class="star full">★</span>';
                                                    }
                                                    
                                                    // Half star
                                                    if ($half_star) {
                                                        echo '<span class="star half">★</span>';
                                                    }
                                                    
                                                    // Empty stars
                                                    for ($i = 0; $i < $empty_stars; $i++) {
                                                        echo '<span class="star empty">☆</span>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    
                                    <?php
                                    $categories = get_the_terms(get_the_ID(), 'ai_category');
                                    if ($categories && !is_wp_error($categories)) :
                                    ?>
                                        <div class="tool-categories">
                                            <?php foreach ($categories as $category) : ?>
                                                <a href="<?php echo get_term_link($category); ?>" class="tool-category-link">
                                                    <?php echo $category->name; ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ( function_exists('get_field') && get_field('affiliate_link') ) : ?>
                                    <div class="tool-cta">
                                        <a href="<?php echo esc_url(get_field('affiliate_link')); ?>" class="tool-cta-button" target="_blank" rel="nofollow noopener">
                                            <?php _e('Get This Tool', 'inspiredshifter-child'); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-content-wrapper">
                    <div class="container">
                        <div class="tool-content-grid">
                            <div class="tool-main-content">
                                <?php if ( function_exists('get_field') ) : ?>
                                    <?php if ( get_field('pros') || get_field('cons') ) : ?>
                                        <div class="tool-pros-cons">
                                            <h2><?php _e('Pros & Cons', 'inspiredshifter-child'); ?></h2>
                                            
                                            <div class="pros-cons-grid">
                                                <?php if ( get_field('pros') ) : ?>
                                                    <div class="pros-column">
                                                        <h3><?php _e('Pros', 'inspiredshifter-child'); ?></h3>
                                                        <ul class="pros-list">
                                                            <?php foreach ( get_field('pros') as $pro ) : ?>
                                                                <li><?php echo $pro['pro_item']; ?></li>
                                                            <?php endforeach; ?>
                                                        </ul>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ( get_field('cons') ) : ?>
                                                    <div class="cons-column">
                                                        <h3><?php _e('Cons', 'inspiredshifter-child'); ?></h3>
                                                        <ul class="cons-list">
                                                            <?php foreach ( get_field('cons') as $con ) : ?>
                                                                <li><?php echo $con['con_item']; ?></li>
                                                            <?php endforeach; ?>
                                                        </ul>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ( get_field('features') ) : ?>
                                        <div class="tool-features">
                                            <h2><?php _e('Key Features', 'inspiredshifter-child'); ?></h2>
                                            
                                            <div class="features-list">
                                                <?php foreach ( get_field('features') as $feature ) : ?>
                                                    <div class="feature-item">
                                                        <h3><?php echo $feature['feature_title']; ?></h3>
                                                        <?php if ( isset($feature['feature_description']) ) : ?>
                                                            <p><?php echo $feature['feature_description']; ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                                
                                <div class="tool-description">
                                    <h2><?php _e('About This Tool', 'inspiredshifter-child'); ?></h2>
                                    <?php the_content(); ?>
                                </div>
                                
                                <?php if ( function_exists('get_field') && get_field('tool_screenshots') ) : ?>
                                    <div class="tool-screenshots">
                                        <h2><?php _e('Screenshots', 'inspiredshifter-child'); ?></h2>
                                        
                                        <div class="screenshots-gallery">
                                            <?php foreach ( get_field('tool_screenshots') as $screenshot ) : ?>
                                                <div class="screenshot-item">
                                                    <img src="<?php echo esc_url($screenshot['screenshot']['url']); ?>" alt="<?php echo esc_attr($screenshot['caption']); ?>">
                                                    <?php if ( $screenshot['caption'] ) : ?>
                                                        <p class="screenshot-caption"><?php echo $screenshot['caption']; ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ( function_exists('get_field') && get_field('pricing') ) : ?>
                                    <div class="tool-pricing-plans">
                                        <h2><?php _e('Pricing Plans', 'inspiredshifter-child'); ?></h2>
                                        
                                        <div class="pricing-plans-grid">
                                            <?php foreach ( get_field('pricing') as $plan ) : ?>
                                                <div class="pricing-plan">
                                                    <h3 class="plan-name"><?php echo $plan['plan_name']; ?></h3>
                                                    
                                                    <div class="plan-price">
                                                        <span class="price-amount">$<?php echo $plan['price']; ?></span>
                                                        <span class="price-period">
                                                            <?php 
                                                            if ( $plan['billing_period'] == 'month' ) {
                                                                _e('/month', 'inspiredshifter-child');
                                                            } elseif ( $plan['billing_period'] == 'year' ) {
                                                                _e('/year', 'inspiredshifter-child');
                                                            } elseif ( $plan['billing_period'] == 'lifetime' ) {
                                                                _e(' (one-time)', 'inspiredshifter-child');
                                                            }
                                                            ?>
                                                        </span>
                                                    </div>
                                                    
                                                    <?php if ( isset($plan['plan_features']) ) : ?>
                                                        <ul class="plan-features">
                                                            <?php foreach ( $plan['plan_features'] as $feature ) : ?>
                                                                <li><?php echo $feature['feature']; ?></li>
                                                            <?php endforeach; ?>
                                                        </ul>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ( function_exists('get_field') && get_field('affiliate_link') ) : ?>
                                                        <a href="<?php echo esc_url(get_field('affiliate_link')); ?>" class="plan-cta-button" target="_blank" rel="nofollow noopener">
                                                            <?php _e('Get Started', 'inspiredshifter-child'); ?>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="tool-sidebar">
                                <?php if ( function_exists('get_field') && get_field('affiliate_link') ) : ?>
                                    <div class="sidebar-cta">
                                        <h3><?php _e('Ready to Try?', 'inspiredshifter-child'); ?></h3>
                                        <p><?php _e('Get started with this tool today and boost your productivity.', 'inspiredshifter-child'); ?></p>
                                        <a href="<?php echo esc_url(get_field('affiliate_link')); ?>" class="sidebar-cta-button" target="_blank" rel="nofollow noopener">
                                            <?php _e('Get This Tool', 'inspiredshifter-child'); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="related-tools">
                                    <h3><?php _e('Related Tools', 'inspiredshifter-child'); ?></h3>
                                    
                                    <?php
                                    $categories = get_the_terms(get_the_ID(), 'ai_category');
                                    $category_ids = array();
                                    
                                    if ($categories && !is_wp_error($categories)) {
                                        foreach ($categories as $category) {
                                            $category_ids[] = $category->term_id;
                                        }
                                    }
                                    
                                    $related_args = array(
                                        'post_type' => 'ai_tool',
                                        'posts_per_page' => 3,
                                        'post__not_in' => array(get_the_ID()),
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'ai_category',
                                                'field' => 'term_id',
                                                'terms' => $category_ids,
                                            ),
                                        ),
                                    );
                                    
                                    $related_tools = new WP_Query($related_args);
                                    
                                    if ($related_tools->have_posts()) :
                                        while ($related_tools->have_posts()) : $related_tools->the_post();
                                    ?>
                                        <div class="related-tool-item">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <img src="<?php the_post_thumbnail_url('thumbnail'); ?>" alt="<?php the_title(); ?>" class="related-tool-image">
                                            <?php endif; ?>
                                            
                                            <div class="related-tool-info">
                                                <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                                <?php if (function_exists('get_field') && get_field('short_description')) : ?>
                                                    <p><?php echo wp_trim_words(get_field('short_description'), 10); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php
                                        endwhile;
                                        wp_reset_postdata();
                                    else :
                                    ?>
                                        <p><?php _e('No related tools found.', 'inspiredshifter-child'); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

        <?php endwhile; ?>

    </main><!-- #main -->
</div><!-- #primary -->

<style>
/* Single AI Tool Styles */
.ai-tool-single {
    margin-bottom: 60px;
}

.tool-hero {
    background: linear-gradient(135deg, var(--background-gray) 0%, #ffffff 100%);
    padding: 60px 0;
    margin-bottom: 40px;
}

.tool-hero-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.tool-logo {
    flex-shrink: 0;
}

.tool-logo img {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tool-logo-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 36px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tool-header-info {
    flex-grow: 1;
}

.tool-title {
    margin-bottom: 10px;
    font-size: 32px;
}

.tool-short-description {
    margin-bottom: 20px;
    font-size: 18px;
    color: var(--text-gray);
}

.tool-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.tool-pricing-badge span {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: white;
}

.pricing-free {
    background-color: var(--teal-accent);
}

.pricing-freemium {
    background-color: var(--purple-accent);
}

.pricing-paid {
    background-color: var(--orange-accent);
}

.tool-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-value {
    font-weight: 600;
}

.rating-stars {
    display: flex;
}

.star {
    color: #ffc107;
    font-size: 18px;
}

.star.empty {
    color: #e0e0e0;
}

.tool-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-category-link {
    display: inline-block;
    padding: 5px 12px;
    background-color: var(--background-gray);
    border-radius: 20px;
    font-size: 14px;
    color: var(--text-gray);
    text-decoration: none;
}

.tool-category-link:hover {
    background-color: var(--primary-blue);
    color: white;
}

.tool-cta {
    margin-top: 20px;
}

.tool-cta-button {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-blue);
    color: white;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: 0 4px 10px rgba(58, 92, 201, 0.2);
}

.tool-cta-button:hover {
    background-color: var(--dark-blue);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(58, 92, 201, 0.25);
}

.tool-content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.tool-main-content > div {
    margin-bottom: 40px;
}

.tool-main-content h2 {
    margin-bottom: 20px;
    font-size: 24px;
    position: relative;
    padding-bottom: 10px;
}

.tool-main-content h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-blue);
}

.pros-cons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.pros-column h3,
.cons-column h3 {
    margin-bottom: 15px;
    font-size: 18px;
}

.pros-list li,
.cons-list li {
    margin-bottom: 10px;
    padding-left: 25px;
    position: relative;
}

.pros-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--teal-accent);
    font-weight: bold;
}

.cons-list li::before {
    content: '✗';
    position: absolute;
    left: 0;
    color: var(--orange-accent);
    font-weight: bold;
}

.features-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.feature-item {
    background-color: var(--background-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
}

.feature-item h3 {
    margin-bottom: 10px;
    font-size: 18px;
}

.screenshots-gallery {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.screenshot-item img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
}

.screenshot-caption {
    margin-top: 10px;
    font-size: 14px;
    color: var(--text-gray);
    text-align: center;
}

.pricing-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.pricing-plan {
    background-color: white;
    border: 1px solid var(--border-gray);
    border-radius: var(--border-radius-md);
    padding: 30px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition-medium);
}

.pricing-plan:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.plan-name {
    margin-bottom: 15px;
    font-size: 20px;
}

.plan-price {
    margin-bottom: 20px;
}

.price-amount {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-blue);
}

.price-period {
    font-size: 14px;
    color: var(--text-gray);
}

.plan-features {
    margin-bottom: 25px;
    text-align: left;
}

.plan-features li {
    margin-bottom: 10px;
    padding-left: 25px;
    position: relative;
}

.plan-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--teal-accent);
}

.plan-cta-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-blue);
    color: white;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
}

.plan-cta-button:hover {
    background-color: var(--dark-blue);
    color: white;
}

.tool-sidebar > div {
    margin-bottom: 40px;
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: 25px;
    box-shadow: var(--box-shadow);
}

.sidebar-cta {
    background-color: var(--background-gray);
    text-align: center;
}

.sidebar-cta h3 {
    margin-bottom: 15px;
    font-size: 20px;
}

.sidebar-cta p {
    margin-bottom: 20px;
}

.sidebar-cta-button {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-blue);
    color: white;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: 0 4px 10px rgba(58, 92, 201, 0.2);
}

.sidebar-cta-button:hover {
    background-color: var(--dark-blue);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(58, 92, 201, 0.25);
}

.related-tools h3 {
    margin-bottom: 20px;
    font-size: 20px;
}

.related-tool-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-gray);
}

.related-tool-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.related-tool-image {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-sm);
    object-fit: cover;
}

.related-tool-info h4 {
    margin-bottom: 5px;
    font-size: 16px;
}

.related-tool-info p {
    font-size: 14px;
    color: var(--text-gray);
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .tool-hero-content {
        flex-direction: column;
        text-align: center;
    }
    
    .tool-meta {
        justify-content: center;
    }
    
    .tool-content-grid {
        grid-template-columns: 1fr;
    }
    
    .pros-cons-grid,
    .features-list,
    .screenshots-gallery {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>