const fetch = require('node-fetch');

async function publishToWordPress(title, content, wpUsername, wpPassword) {
  try {
    if (!wpUsername || !wpPassword) {
      throw new Error('WordPress credentials are not configured');
    }

    const response = await fetch('https://inspiredshifter.com/wp-json/wp/v2/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + Buffer.from(`${wpUsername}:${wpPassword}`).toString('base64')
      },
      body: JSON.stringify({
        title: title,
        content: content,
        status: 'publish'
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`WordPress error: ${errorData.message || 'Unknown error'}`);
    }

    const data = await response.json();
    return {
      success: true,
      postId: data.id,
      postUrl: data.link
    };
  } catch (error) {
    console.error('WordPress publishing error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = { publishToWordPress };
