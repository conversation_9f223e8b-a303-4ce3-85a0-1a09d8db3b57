* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

section {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

input, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

#edit-btn, #cancel-edit-btn {
    background-color: #95a5a6;
    margin-right: 10px;
}

#edit-btn:hover, #cancel-edit-btn:hover {
    background-color: #7f8c8d;
}

#publish-btn {
    background-color: #27ae60;
}

#publish-btn:hover {
    background-color: #2ecc71;
}

.hidden {
    display: none;
}

.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#status-message {
    text-align: center;
    font-size: 18px;
}

#screenshot-container {
    margin-bottom: 30px;
}

#tool-screenshot {
    max-width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#content-preview {
    background-color: #f8f9fa;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

#result-message {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
}

footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: #7f8c8d;
}
