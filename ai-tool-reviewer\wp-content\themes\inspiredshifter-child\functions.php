<?php
/**
 * InspiredShifter Child Theme functions and definitions
 *
 * @package InspiredShifter Child
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles
 */
function inspiredshifter_child_enqueue_styles() {
    // Enqueue parent theme style
    wp_enqueue_style('astra-style', get_template_directory_uri() . '/style.css', array(), wp_get_theme('astra')->get('Version'));
    
    // Enqueue child theme style
    wp_enqueue_style('inspiredshifter-child-style', get_stylesheet_uri(), array('astra-style'), wp_get_theme()->get('Version'));
    
    // Enqueue modern styles
    wp_enqueue_style('inspiredshifter-modern-styles', get_stylesheet_directory_uri() . '/assets/css/modern-styles.css', array('inspiredshifter-child-style'), wp_get_theme()->get('Version'));
    
    // Enqueue Google Fonts
    wp_enqueue_style('inspiredshifter-fonts', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600&display=swap', array(), null);
    
    // Enqueue custom JavaScript
    wp_enqueue_script('inspiredshifter-js', get_stylesheet_directory_uri() . '/assets/js/main.js', array('jquery'), wp_get_theme()->get('Version'), true);
}
add_action('wp_enqueue_scripts', 'inspiredshifter_child_enqueue_styles');

/**
 * Register Custom Post Types and Taxonomies
 */
function inspiredshifter_register_post_types() {
    // AI Tool Custom Post Type
    $labels = array(
        'name'               => _x('AI Tools', 'post type general name', 'inspiredshifter-child'),
        'singular_name'      => _x('AI Tool', 'post type singular name', 'inspiredshifter-child'),
        'menu_name'          => _x('AI Tools', 'admin menu', 'inspiredshifter-child'),
        'name_admin_bar'     => _x('AI Tool', 'add new on admin bar', 'inspiredshifter-child'),
        'add_new'            => _x('Add New', 'ai_tool', 'inspiredshifter-child'),
        'add_new_item'       => __('Add New AI Tool', 'inspiredshifter-child'),
        'new_item'           => __('New AI Tool', 'inspiredshifter-child'),
        'edit_item'          => __('Edit AI Tool', 'inspiredshifter-child'),
        'view_item'          => __('View AI Tool', 'inspiredshifter-child'),
        'all_items'          => __('All AI Tools', 'inspiredshifter-child'),
        'search_items'       => __('Search AI Tools', 'inspiredshifter-child'),
        'parent_item_colon'  => __('Parent AI Tools:', 'inspiredshifter-child'),
        'not_found'          => __('No AI tools found.', 'inspiredshifter-child'),
        'not_found_in_trash' => __('No AI tools found in Trash.', 'inspiredshifter-child')
    );

    $args = array(
        'labels'             => $labels,
        'description'        => __('AI tools and services', 'inspiredshifter-child'),
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'ai-tool'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-superhero',
        'supports'           => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields')
    );

    register_post_type('ai_tool', $args);
    
    // AI Category Taxonomy
    $category_labels = array(
        'name'              => _x('AI Categories', 'taxonomy general name', 'inspiredshifter-child'),
        'singular_name'     => _x('AI Category', 'taxonomy singular name', 'inspiredshifter-child'),
        'search_items'      => __('Search AI Categories', 'inspiredshifter-child'),
        'all_items'         => __('All AI Categories', 'inspiredshifter-child'),
        'parent_item'       => __('Parent AI Category', 'inspiredshifter-child'),
        'parent_item_colon' => __('Parent AI Category:', 'inspiredshifter-child'),
        'edit_item'         => __('Edit AI Category', 'inspiredshifter-child'),
        'update_item'       => __('Update AI Category', 'inspiredshifter-child'),
        'add_new_item'      => __('Add New AI Category', 'inspiredshifter-child'),
        'new_item_name'     => __('New AI Category Name', 'inspiredshifter-child'),
        'menu_name'         => __('AI Categories', 'inspiredshifter-child'),
    );

    $category_args = array(
        'hierarchical'      => true,
        'labels'            => $category_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ai-category'),
    );

    register_taxonomy('ai_category', array('ai_tool'), $category_args);
    
    // AI Feature Taxonomy
    $feature_labels = array(
        'name'              => _x('AI Features', 'taxonomy general name', 'inspiredshifter-child'),
        'singular_name'     => _x('AI Feature', 'taxonomy singular name', 'inspiredshifter-child'),
        'search_items'      => __('Search AI Features', 'inspiredshifter-child'),
        'all_items'         => __('All AI Features', 'inspiredshifter-child'),
        'parent_item'       => __('Parent AI Feature', 'inspiredshifter-child'),
        'parent_item_colon' => __('Parent AI Feature:', 'inspiredshifter-child'),
        'edit_item'         => __('Edit AI Feature', 'inspiredshifter-child'),
        'update_item'       => __('Update AI Feature', 'inspiredshifter-child'),
        'add_new_item'      => __('Add New AI Feature', 'inspiredshifter-child'),
        'new_item_name'     => __('New AI Feature Name', 'inspiredshifter-child'),
        'menu_name'         => __('AI Features', 'inspiredshifter-child'),
    );

    $feature_args = array(
        'hierarchical'      => false,
        'labels'            => $feature_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ai-feature'),
    );

    register_taxonomy('ai_feature', array('ai_tool'), $feature_args);
    
    // AI Pricing Taxonomy
    $pricing_labels = array(
        'name'              => _x('Pricing Models', 'taxonomy general name', 'inspiredshifter-child'),
        'singular_name'     => _x('Pricing Model', 'taxonomy singular name', 'inspiredshifter-child'),
        'search_items'      => __('Search Pricing Models', 'inspiredshifter-child'),
        'all_items'         => __('All Pricing Models', 'inspiredshifter-child'),
        'parent_item'       => __('Parent Pricing Model', 'inspiredshifter-child'),
        'parent_item_colon' => __('Parent Pricing Model:', 'inspiredshifter-child'),
        'edit_item'         => __('Edit Pricing Model', 'inspiredshifter-child'),
        'update_item'       => __('Update Pricing Model', 'inspiredshifter-child'),
        'add_new_item'      => __('Add New Pricing Model', 'inspiredshifter-child'),
        'new_item_name'     => __('New Pricing Model Name', 'inspiredshifter-child'),
        'menu_name'         => __('Pricing Models', 'inspiredshifter-child'),
    );

    $pricing_args = array(
        'hierarchical'      => true,
        'labels'            => $pricing_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ai-pricing'),
    );

    register_taxonomy('ai_pricing', array('ai_tool'), $pricing_args);
}
add_action('init', 'inspiredshifter_register_post_types');

/**
 * Register ACF fields if ACF is active
 * Only register if ACF is active to prevent errors
 */
function inspiredshifter_register_acf_fields() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }
    
    // AI Tool Fields - Basic version with fewer fields to prevent issues
    acf_add_local_field_group(array(
        'key' => 'group_ai_tool_fields',
        'title' => 'AI Tool Details',
        'fields' => array(
            array(
                'key' => 'field_tool_name',
                'label' => 'Tool Name',
                'name' => 'tool_name',
                'type' => 'text',
                'instructions' => 'Enter the name of the AI tool',
                'required' => 1,
            ),
            array(
                'key' => 'field_short_description',
                'label' => 'Short Description',
                'name' => 'short_description',
                'type' => 'textarea',
                'instructions' => 'Enter a brief description (max 160 characters)',
                'required' => 1,
                'maxlength' => 160,
            ),
            array(
                'key' => 'field_pricing_type',
                'label' => 'Pricing Type',
                'name' => 'pricing_type',
                'type' => 'select',
                'instructions' => 'Select the pricing model',
                'required' => 1,
                'choices' => array(
                    'free' => 'Free',
                    'freemium' => 'Freemium',
                    'paid' => 'Paid',
                ),
                'default_value' => 'freemium',
            ),
            array(
                'key' => 'field_starting_price',
                'label' => 'Starting Price',
                'name' => 'starting_price',
                'type' => 'number',
                'instructions' => 'Enter the starting price (if applicable)',
                'required' => 0,
            ),
            array(
                'key' => 'field_is_popular',
                'label' => 'Popular Tool',
                'name' => 'is_popular',
                'type' => 'true_false',
                'instructions' => 'Mark as popular tool',
                'required' => 0,
                'ui' => 1,
            ),
            array(
                'key' => 'field_is_featured',
                'label' => 'Featured Tool',
                'name' => 'is_featured',
                'type' => 'true_false',
                'instructions' => 'Show on homepage',
                'required' => 0,
                'ui' => 1,
            ),
            array(
                'key' => 'field_affiliate_link',
                'label' => 'Affiliate Link',
                'name' => 'affiliate_link',
                'type' => 'url',
                'instructions' => 'Enter affiliate link',
                'required' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'ai_tool',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
    
    // AI Category Fields
    acf_add_local_field_group(array(
        'key' => 'group_ai_category_fields',
        'title' => 'Category Details',
        'fields' => array(
            array(
                'key' => 'field_category_color',
                'label' => 'Category Color',
                'name' => 'category_color',
                'type' => 'color_picker',
                'instructions' => 'Choose a color for this category',
                'required' => 0,
                'default_value' => '#3a5cc9',
            ),
            array(
                'key' => 'field_category_image',
                'label' => 'Category Image',
                'name' => 'category_image',
                'type' => 'image',
                'instructions' => 'Upload an image for this category',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'ai_category',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
}
add_action('init', 'inspiredshifter_register_acf_fields');

/**
 * Create placeholder images directory
 */
function inspiredshifter_create_placeholder_images() {
    $upload_dir = wp_upload_dir();
    $images_dir = get_stylesheet_directory() . '/assets/images';
    
    // Create the directory if it doesn't exist
    if (!file_exists($images_dir)) {
        wp_mkdir_p($images_dir);
    }
}
add_action('after_setup_theme', 'inspiredshifter_create_placeholder_images');

/**
 * Add affiliate link shortcode
 */
function inspiredshifter_affiliate_link_shortcode($atts) {
    $atts = shortcode_atts(array(
        'url' => '',
        'text' => 'Learn More',
        'class' => '',
    ), $atts);
    
    if (empty($atts['url'])) {
        return '';
    }
    
    $class = !empty($atts['class']) ? ' class="' . esc_attr($atts['class']) . '"' : '';
    
    return '<a href="' . esc_url($atts['url']) . '"' . $class . ' target="_blank" rel="nofollow noopener">' . esc_html($atts['text']) . '</a>';
}
add_shortcode('affiliate_link', 'inspiredshifter_affiliate_link_shortcode');

/**
 * Register navigation menus
 */
function inspiredshifter_register_menus() {
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'inspiredshifter-child'),
        'footer' => __('Footer Menu', 'inspiredshifter-child'),
    ));
}
add_action('after_setup_theme', 'inspiredshifter_register_menus');
