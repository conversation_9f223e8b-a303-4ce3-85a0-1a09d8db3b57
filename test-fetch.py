import requests
import json

# Define the URL to fetch
url_to_fetch = "https://www.example.com"

# Define the MCP server endpoint
# Try different endpoint paths
endpoints = [
    "http://localhost:8080/tools/fetch",
    "http://localhost:8080/fetch",
    "http://localhost:8080/api/fetch",
    "http://localhost:8080/v1/fetch",
    "http://localhost:8080/mcp/fetch",
    "http://localhost:8000/tools/fetch",
    "http://localhost:8000/fetch",
    "http://localhost:8000/api/fetch",
    "http://localhost:8000/v1/fetch",
    "http://localhost:8000/mcp/fetch"
]

# Prepare the request payload
payload = {
    "url": url_to_fetch,
    "max_length": 5000,
    "start_index": 0,
    "raw": False
}

# Try each endpoint
for endpoint in endpoints:
    try:
        print(f"\nTrying endpoint: {endpoint}")
        response = requests.post(endpoint, json=payload, timeout=5)
        print(f"Status code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("\nFetched content:")
            print(result.get("content", "No content found"))
            break  # Found a working endpoint, no need to try others
        else:
            print(f"Response: {response.text[:100]}...")  # Show only first 100 chars
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
