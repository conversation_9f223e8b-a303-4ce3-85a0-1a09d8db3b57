<?php
/**
 * The template for displaying AI Category taxonomy archives
 *
 * @package InspiredShifter Child
 */

get_header();

// Get the current category
$current_category = get_queried_object();

// Get category color and image if ACF is active
$category_color = '#3a5cc9'; // Default color
$category_image = '';

if (function_exists('get_field')) {
    $cat_color = get_field('category_color', $current_category);
    if ($cat_color) {
        $category_color = $cat_color;
    }
    
    $cat_image = get_field('category_image', $current_category);
    if ($cat_image && isset($cat_image['url'])) {
        $category_image = $cat_image['url'];
    }
}
?>

<div class="category-header" style="background-color: <?php echo esc_attr($category_color); ?>10;">
    <div class="container">
        <div class="category-header-content">
            <?php if ($category_image) : ?>
                <div class="category-image">
                    <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($current_category->name); ?>">
                </div>
            <?php else : ?>
                <div class="category-icon" style="background-color: <?php echo esc_attr($category_color); ?>;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>
                </div>
            <?php endif; ?>
            
            <div class="category-info">
                <h1 class="category-title"><?php echo esc_html($current_category->name); ?></h1>
                
                <?php if ($current_category->description) : ?>
                    <div class="category-description">
                        <?php echo wp_kses_post($current_category->description); ?>
                    </div>
                <?php endif; ?>
                
                <div class="category-meta">
                    <span class="tool-count"><?php echo sprintf(_n('%s Tool', '%s Tools', $current_category->count, 'inspiredshifter-child'), number_format_i18n($current_category->count)); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="category-filters">
    <div class="container">
        <div class="filters-wrapper">
            <div class="filter-group">
                <label for="price-filter"><?php _e('Price:', 'inspiredshifter-child'); ?></label>
                <select id="price-filter" class="filter-select">
                    <option value="all"><?php _e('All', 'inspiredshifter-child'); ?></option>
                    <option value="free"><?php _e('Free', 'inspiredshifter-child'); ?></option>
                    <option value="freemium"><?php _e('Freemium', 'inspiredshifter-child'); ?></option>
                    <option value="paid"><?php _e('Paid', 'inspiredshifter-child'); ?></option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="rating-filter"><?php _e('Rating:', 'inspiredshifter-child'); ?></label>
                <select id="rating-filter" class="filter-select">
                    <option value="all"><?php _e('All', 'inspiredshifter-child'); ?></option>
                    <option value="4"><?php _e('4+ Stars', 'inspiredshifter-child'); ?></option>
                    <option value="3"><?php _e('3+ Stars', 'inspiredshifter-child'); ?></option>
                    <option value="2"><?php _e('2+ Stars', 'inspiredshifter-child'); ?></option>
                </select>
            </div>
            
            <?php
            // Get features for this category
            $features = get_terms(array(
                'taxonomy' => 'ai_feature',
                'hide_empty' => true,
            ));
            
            if (!empty($features) && !is_wp_error($features)) :
            ?>
                <div class="filter-group">
                    <label for="feature-filter"><?php _e('Feature:', 'inspiredshifter-child'); ?></label>
                    <select id="feature-filter" class="filter-select">
                        <option value="all"><?php _e('All', 'inspiredshifter-child'); ?></option>
                        <?php foreach ($features as $feature) : ?>
                            <option value="<?php echo esc_attr($feature->slug); ?>"><?php echo esc_html($feature->name); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="category-content">
    <div class="container">
        <?php if (have_posts()) : ?>
            <div class="tools-grid">
                <?php while (have_posts()) : the_post(); 
                    // Get tool data for filtering
                    $pricing_type = '';
                    $rating = 0;
                    $features_array = array();
                    
                    if (function_exists('get_field')) {
                        $pricing_type = get_field('pricing_type');
                        $rating = get_field('rating') ? get_field('rating') : 0;
                    }
                    
                    $tool_features = get_the_terms(get_the_ID(), 'ai_feature');
                    if ($tool_features && !is_wp_error($tool_features)) {
                        foreach ($tool_features as $feature) {
                            $features_array[] = $feature->slug;
                        }
                    }
                    
                    $features_string = implode(',', $features_array);
                ?>
                    <div class="tool-card modern-tool-card" data-price="<?php echo esc_attr($pricing_type); ?>" data-rating="<?php echo esc_attr($rating); ?>" data-features="<?php echo esc_attr($features_string); ?>">
                        <div class="tool-header">
                            <?php if (has_post_thumbnail()) : ?>
                                <img src="<?php the_post_thumbnail_url('thumbnail'); ?>" alt="<?php the_title(); ?>" class="tool-logo">
                            <?php else: ?>
                                <div class="tool-logo-placeholder" style="background-color: <?php echo esc_attr($category_color); ?>;">
                                    <?php echo substr(get_the_title(), 0, 2); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (function_exists('get_field') && get_field('is_popular')) : ?>
                                <span class="popular-badge"><?php _e('Popular', 'inspiredshifter-child'); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <h3 class="tool-name"><?php the_title(); ?></h3>
                        <p class="tool-description"><?php 
                            if (function_exists('get_field') && get_field('short_description')) {
                                echo get_field('short_description');
                            } else {
                                echo get_the_excerpt();
                            }
                        ?></p>
                        
                        <div class="tool-footer">
                            <div class="pricing-info">
                                <?php 
                                if (function_exists('get_field')) {
                                    $pricing_type = get_field('pricing_type');
                                    if ($pricing_type == 'free') {
                                        _e('Free', 'inspiredshifter-child');
                                    } elseif ($pricing_type == 'freemium') {
                                        _e('Free & Paid Plans', 'inspiredshifter-child');
                                    } elseif ($pricing_type == 'paid') {
                                        $starting_price = get_field('starting_price');
                                        if ($starting_price) {
                                            echo 'From $' . $starting_price . '/mo';
                                        } else {
                                            _e('Paid', 'inspiredshifter-child');
                                        }
                                    } else {
                                        _e('View Pricing', 'inspiredshifter-child');
                                    }
                                } else {
                                    _e('View Details', 'inspiredshifter-child');
                                }
                                ?>
                            </div>
                            <a href="<?php the_permalink(); ?>" class="learn-more-btn"><?php _e('Learn More', 'inspiredshifter-child'); ?></a>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
            
            <div class="pagination">
                <?php
                echo paginate_links(array(
                    'prev_text' => __('&laquo; Previous', 'inspiredshifter-child'),
                    'next_text' => __('Next &raquo;', 'inspiredshifter-child'),
                ));
                ?>
            </div>
        <?php else : ?>
            <div class="no-tools-message">
                <p><?php _e('No tools found in this category. Check back soon!', 'inspiredshifter-child'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Category Archive Styles */
.category-header {
    padding: 60px 0;
    margin-bottom: 40px;
}

.category-header-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.category-image img {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-icon {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-info {
    flex-grow: 1;
}

.category-title {
    margin-bottom: 15px;
    font-size: 32px;
    color: var(--title-gray);
}

.category-description {
    margin-bottom: 20px;
    font-size: 16px;
    color: var(--text-gray);
    max-width: 800px;
}

.category-meta {
    font-size: 14px;
    color: var(--text-gray);
}

.category-filters {
    background-color: white;
    padding: 20px 0;
    margin-bottom: 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--title-gray);
}

.filter-select {
    padding: 8px 15px;
    border: 1px solid var(--border-gray);
    border-radius: var(--border-radius-sm);
    background-color: white;
    color: var(--text-gray);
    font-family: var(--body-font);
    font-size: 14px;
    transition: var(--transition-fast);
}

.filter-select:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(58, 92, 201, 0.1);
}

.category-content {
    padding-bottom: 80px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.pagination {
    text-align: center;
}

.pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 5px;
    border: 1px solid var(--border-gray);
    border-radius: var(--border-radius-sm);
    color: var(--text-gray);
    text-decoration: none;
    transition: var(--transition-fast);
}

.pagination .page-numbers.current {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.pagination .page-numbers:hover:not(.current) {
    background-color: var(--background-gray);
}

.no-tools-message {
    text-align: center;
    padding: 40px 0;
    color: var(--text-gray);
}

@media (max-width: 1024px) {
    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .category-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .category-description {
        margin-left: auto;
        margin-right: auto;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>