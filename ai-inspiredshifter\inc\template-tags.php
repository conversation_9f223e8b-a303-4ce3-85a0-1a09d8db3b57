<?php
/**
 * Custom template tags for this theme
 *
 * @package AI_InspiredShifter
 */

if (!function_exists('ai_inspiredshifter_posted_on')) :
    /**
     * Prints HTML with meta information for the current post-date/time.
     */
    function ai_inspiredshifter_posted_on() {
        $time_string = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
        if (get_the_time('U') !== get_the_modified_time('U')) {
            $time_string = '<time class="entry-date published" datetime="%1$s">%2$s</time><time class="updated" datetime="%3$s">%4$s</time>';
        }

        $time_string = sprintf(
            $time_string,
            esc_attr(get_the_date(DATE_W3C)),
            esc_html(get_the_date()),
            esc_attr(get_the_modified_date(DATE_W3C)),
            esc_html(get_the_modified_date())
        );

        $posted_on = sprintf(
            /* translators: %s: post date. */
            esc_html_x('Posted on %s', 'post date', 'ai-inspiredshifter'),
            '<a href="' . esc_url(get_permalink()) . '" rel="bookmark">' . $time_string . '</a>'
        );

        echo '<span class="posted-on">' . $posted_on . '</span>'; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
    }
endif;

if (!function_exists('ai_inspiredshifter_posted_by')) :
    /**
     * Prints HTML with meta information for the current author.
     */
    function ai_inspiredshifter_posted_by() {
        $byline = sprintf(
            /* translators: %s: post author. */
            esc_html_x('by %s', 'post author', 'ai-inspiredshifter'),
            '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
        );

        echo '<span class="byline"> ' . $byline . '</span>'; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
    }
endif;

if (!function_exists('ai_inspiredshifter_entry_footer')) :
    /**
     * Prints HTML with meta information for the categories, tags and comments.
     */
    function ai_inspiredshifter_entry_footer() {
        // Hide category and tag text for pages.
        if ('post' === get_post_type()) {
            /* translators: used between list items, there is a space after the comma */
            $categories_list = get_the_category_list(esc_html__(', ', 'ai-inspiredshifter'));
            if ($categories_list) {
                /* translators: 1: list of categories. */
                printf('<span class="cat-links">' . esc_html__('Posted in %1$s', 'ai-inspiredshifter') . '</span>', $categories_list); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
            }

            /* translators: used between list items, there is a space after the comma */
            $tags_list = get_the_tag_list('', esc_html_x(', ', 'list item separator', 'ai-inspiredshifter'));
            if ($tags_list) {
                /* translators: 1: list of tags. */
                printf('<span class="tags-links">' . esc_html__('Tagged %1$s', 'ai-inspiredshifter') . '</span>', $tags_list); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
            }
        }

        if (!is_single() && !post_password_required() && (comments_open() || get_comments_number())) {
            echo '<span class="comments-link">';
            comments_popup_link(
                sprintf(
                    wp_kses(
                        /* translators: %s: post title */
                        __('Leave a Comment<span class="screen-reader-text"> on %s</span>', 'ai-inspiredshifter'),
                        array(
                            'span' => array(
                                'class' => array(),
                            ),
                        )
                    ),
                    wp_kses_post(get_the_title())
                )
            );
            echo '</span>';
        }

        edit_post_link(
            sprintf(
                wp_kses(
                    /* translators: %s: Name of current post. Only visible to screen readers */
                    __('Edit <span class="screen-reader-text">%s</span>', 'ai-inspiredshifter'),
                    array(
                        'span' => array(
                            'class' => array(),
                        ),
                    )
                ),
                wp_kses_post(get_the_title())
            ),
            '<span class="edit-link">',
            '</span>'
        );
    }
endif;

if (!function_exists('ai_inspiredshifter_post_thumbnail')) :
    /**
     * Displays an optional post thumbnail.
     *
     * Wraps the post thumbnail in an anchor element on index views, or a div
     * element when on single views.
     */
    function ai_inspiredshifter_post_thumbnail() {
        if (post_password_required() || is_attachment() || !has_post_thumbnail()) {
            return;
        }

        if (is_singular()) :
            ?>

            <div class="post-thumbnail">
                <?php the_post_thumbnail(); ?>
            </div><!-- .post-thumbnail -->

        <?php else : ?>

            <a class="post-thumbnail" href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                <?php
                the_post_thumbnail(
                    'post-thumbnail',
                    array(
                        'alt' => the_title_attribute(
                            array(
                                'echo' => false,
                            )
                        ),
                    )
                );
                ?>
            </a>

            <?php
        endif; // End is_singular().
    }
endif;

if (!function_exists('ai_inspiredshifter_display_rating')) :
    /**
     * Display star rating
     */
    function ai_inspiredshifter_display_rating($rating, $max = 5) {
        if (empty($rating)) {
            return;
        }
        
        $html = '<div class="star-rating">';
        
        // Full stars
        $full_stars = floor($rating);
        for ($i = 0; $i < $full_stars; $i++) {
            $html .= '<i class="fas fa-star"></i>';
        }
        
        // Half star
        $half_star = $rating - $full_stars;
        if ($half_star >= 0.25 && $half_star < 0.75) {
            $html .= '<i class="fas fa-star-half-alt"></i>';
            $empty_stars = $max - $full_stars - 1;
        } elseif ($half_star >= 0.75) {
            $html .= '<i class="fas fa-star"></i>';
            $empty_stars = $max - $full_stars - 1;
        } else {
            $empty_stars = $max - $full_stars;
        }
        
        // Empty stars
        for ($i = 0; $i < $empty_stars; $i++) {
            $html .= '<i class="far fa-star"></i>';
        }
        
        $html .= '<span class="rating-value">' . number_format($rating, 1) . '/' . $max . '</span>';
        $html .= '</div>';
        
        return $html;
    }
endif;

if (!function_exists('ai_inspiredshifter_get_related_posts')) :
    /**
     * Get related posts based on categories
     */
    function ai_inspiredshifter_get_related_posts($post_id, $related_count, $args = array()) {
        $args = wp_parse_args($args, array(
            'orderby' => 'rand',
            'return'  => 'query',
        ));

        $related_args = array(
            'post_type'      => get_post_type($post_id),
            'posts_per_page' => $related_count,
            'post_status'    => 'publish',
            'post__not_in'   => array($post_id),
            'orderby'        => $args['orderby'],
        );

        // Get categories
        $categories = get_the_category($post_id);

        if (!empty($categories)) {
            $category_ids = array();

            foreach ($categories as $category) {
                $category_ids[] = $category->term_id;
            }

            $related_args['category__in'] = $category_ids;
        }

        // Get the posts
        $related_query = new WP_Query($related_args);

        if ($args['return'] === 'query') {
            return $related_query;
        }

        return $related_query->posts;
    }
endif;

if (!function_exists('ai_inspiredshifter_get_tool_features')) :
    /**
     * Get tool features
     */
    function ai_inspiredshifter_get_tool_features($post_id) {
        $features = get_post_meta($post_id, 'tool_features', true);
        
        if (empty($features)) {
            // Fallback to taxonomy
            $feature_terms = get_the_terms($post_id, 'ai_feature');
            
            if (!empty($feature_terms) && !is_wp_error($feature_terms)) {
                $features = array();
                
                foreach ($feature_terms as $term) {
                    $features[] = $term->name;
                }
                
                return $features;
            }
            
            return array();
        }
        
        return explode("\n", $features);
    }
endif;

if (!function_exists('ai_inspiredshifter_get_tool_pros_cons')) :
    /**
     * Get tool pros and cons
     */
    function ai_inspiredshifter_get_tool_pros_cons($post_id) {
        $pros = get_post_meta($post_id, 'tool_pros', true);
        $cons = get_post_meta($post_id, 'tool_cons', true);
        
        $pros_array = !empty($pros) ? explode("\n", $pros) : array();
        $cons_array = !empty($cons) ? explode("\n", $cons) : array();
        
        return array(
            'pros' => $pros_array,
            'cons' => $cons_array,
        );
    }
endif;

if (!function_exists('ai_inspiredshifter_get_tool_pricing_info')) :
    /**
     * Get tool pricing information
     */
    function ai_inspiredshifter_get_tool_pricing_info($post_id) {
        $pricing_type = get_post_meta($post_id, 'tool_pricing_type', true);
        $starting_price = get_post_meta($post_id, 'tool_starting_price', true);
        $pricing_plans = get_post_meta($post_id, 'tool_pricing_plans', true);
        
        // Fallback to taxonomy
        if (empty($pricing_type)) {
            $pricing_models = get_the_terms($post_id, 'pricing_model');
            
            if (!empty($pricing_models) && !is_wp_error($pricing_models)) {
                $pricing_type = $pricing_models[0]->name;
            }
        }
        
        return array(
            'type' => $pricing_type,
            'starting_price' => $starting_price,
            'plans' => $pricing_plans,
        );
    }
endif;
