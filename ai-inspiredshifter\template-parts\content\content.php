<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class('blog-card'); ?>>
    <div class="blog-card-inner">
        <?php if (has_post_thumbnail()) : ?>
            <div class="blog-card-image">
                <a href="<?php the_permalink(); ?>">
                    <?php the_post_thumbnail('medium_large'); ?>
                </a>
                <?php
                // Display categories
                $categories = get_the_category();
                if (!empty($categories)) {
                    echo '<div class="blog-categories">';
                    foreach ($categories as $category) {
                        echo '<a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a>';
                    }
                    echo '</div>';
                }
                ?>
            </div>
        <?php endif; ?>

        <div class="blog-card-content">
            <header class="entry-header">
                <?php
                if (!has_post_thumbnail()) {
                    // Display categories if no featured image
                    $categories = get_the_category();
                    if (!empty($categories)) {
                        echo '<div class="blog-categories">';
                        foreach ($categories as $category) {
                            echo '<a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a>';
                        }
                        echo '</div>';
                    }
                }
                ?>

                <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>

                <div class="entry-meta">
                    <span class="posted-on">
                        <i class="far fa-calendar-alt"></i>
                        <?php echo get_the_date(); ?>
                    </span>
                    <span class="posted-by">
                        <i class="far fa-user"></i>
                        <?php the_author_posts_link(); ?>
                    </span>
                </div><!-- .entry-meta -->
            </header><!-- .entry-header -->

            <div class="entry-content">
                <?php the_excerpt(); ?>
            </div><!-- .entry-content -->

            <footer class="entry-footer">
                <a href="<?php the_permalink(); ?>" class="read-more">
                    <?php esc_html_e('Read More', 'ai-inspiredshifter'); ?> <i class="fas fa-arrow-right"></i>
                </a>
            </footer><!-- .entry-footer -->
        </div>
    </div>
</article><!-- #post-<?php the_ID(); ?> -->
