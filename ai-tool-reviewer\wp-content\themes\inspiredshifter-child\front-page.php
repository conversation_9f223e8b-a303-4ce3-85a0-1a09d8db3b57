<?php
/**
 * Front page template for InspiredShifter Child Theme
 *
 * @package InspiredShifter Child
 */

get_header();
?>

<section class="hero-section modern-hero">
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title"><?php _e('Transform Your Business with Powerful AI Tools', 'inspiredshifter-child'); ?></h1>
            <p class="hero-description"><?php _e('Discover AI solutions that save time, increase productivity, and help you earn more online.', 'inspiredshifter-child'); ?></p>
            <div class="cta-buttons">
                <a href="<?php echo esc_url(home_url('/ai-tools/')); ?>" class="cta-button cta-blue"><?php _e('Explore AI Tools', 'inspiredshifter-child'); ?></a>
                <a href="<?php echo esc_url(home_url('/top-10-ai-tools/')); ?>" class="cta-button cta-white"><?php _e('See Top Picks', 'inspiredshifter-child'); ?></a>
            </div>
        </div>
    </div>
</section>

<section class="featured-tools-section">
    <div class="container">
        <h2 class="section-title"><?php _e('Featured AI Solutions', 'inspiredshifter-child'); ?></h2>
        <p class="section-description"><?php _e('Hand-picked tools to supercharge your content creation, workflow automation, and business growth.', 'inspiredshifter-child'); ?></p>
        
        <div class="tools-grid">
            <?php
            $args = array(
                'post_type' => 'ai_tool',
                'posts_per_page' => 3,
                'meta_query' => array(
                    array(
                        'key' => 'is_featured',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            );
            $featured_tools = new WP_Query($args);
            
            if ($featured_tools->have_posts()) :
                while ($featured_tools->have_posts()) : $featured_tools->the_post();
                    ?>
                    <div class="tool-card modern-tool-card">
                        <div class="tool-header">
                            <?php if (has_post_thumbnail()) : ?>
                                <img src="<?php the_post_thumbnail_url('thumbnail'); ?>" alt="<?php the_title(); ?>" class="tool-logo">
                            <?php else: ?>
                                <div class="tool-logo-placeholder" style="background-color: #3a5cc9;">
                                    <?php echo substr(get_the_title(), 0, 2); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_field('is_popular')) : ?>
                                <span class="popular-badge"><?php _e('Popular', 'inspiredshifter-child'); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <h3 class="tool-name"><?php the_title(); ?></h3>
                        <p class="tool-description"><?php 
                            if (function_exists('get_field') && get_field('short_description')) {
                                echo get_field('short_description');
                            } else {
                                echo get_the_excerpt();
                            }
                        ?></p>
                        
                        <div class="tool-footer">
                            <div class="pricing-info">
                                <?php 
                                if (function_exists('get_field')) {
                                    $pricing_type = get_field('pricing_type');
                                    if ($pricing_type == 'free') {
                                        _e('Free', 'inspiredshifter-child');
                                    } elseif ($pricing_type == 'freemium') {
                                        _e('Free & Paid Plans', 'inspiredshifter-child');
                                    } elseif ($pricing_type == 'paid') {
                                        $starting_price = get_field('starting_price');
                                        if ($starting_price) {
                                            echo 'From $' . $starting_price . '/mo';
                                        } else {
                                            _e('Paid', 'inspiredshifter-child');
                                        }
                                    } else {
                                        _e('View Pricing', 'inspiredshifter-child');
                                    }
                                } else {
                                    _e('View Details', 'inspiredshifter-child');
                                }
                                ?>
                            </div>
                            <a href="<?php the_permalink(); ?>" class="learn-more-btn"><?php _e('Learn More', 'inspiredshifter-child'); ?></a>
                        </div>
                    </div>
                <?php
                endwhile;
                wp_reset_postdata();
            else :
                ?>
                <div class="no-tools-message">
                    <p><?php _e('No featured tools found. Add some AI tools and mark them as featured to display them here.', 'inspiredshifter-child'); ?></p>
                </div>
                <?php
            endif;
            ?>
        </div>
    </div>
</section>

<section class="resources-section">
    <div class="container">
        <h2 class="section-title"><?php _e('Explore More AI Tools & Resources', 'inspiredshifter-child'); ?></h2>
        
        <div class="resources-grid">
            <div class="resource-card">
                <div class="resource-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>
                </div>
                <h3><?php _e('Latest Insights & Tutorials', 'inspiredshifter-child'); ?></h3>
                <p><?php _e('Stay updated with the newest AI trends, tool reviews, and how-to guides on our blog.', 'inspiredshifter-child'); ?></p>
                <a href="<?php echo esc_url(home_url('/blog/')); ?>" class="resource-btn"><?php _e('Read Our Blog', 'inspiredshifter-child'); ?></a>
            </div>
            
            <div class="resource-card">
                <div class="resource-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"></path><path d="M2 17l10 5 10-5"></path><path d="M2 12l10 5 10-5"></path></svg>
                </div>
                <h3><?php _e('Discover More AI Tools', 'inspiredshifter-child'); ?></h3>
                <p><?php _e('Browse our curated directory of AI tools across various categories to find the perfect fit for your needs.', 'inspiredshifter-child'); ?></p>
                <a href="<?php echo esc_url(home_url('/ai-tools/')); ?>" class="resource-btn"><?php _e('Browse AI Directory', 'inspiredshifter-child'); ?></a>
            </div>
        </div>
    </div>
</section>

<section class="categories-section">
    <div class="container">
        <h3 class="section-title"><?php _e('Popular AI Tool Categories', 'inspiredshifter-child'); ?></h3>
        
        <div class="categories-grid">
            <?php
            if (taxonomy_exists('ai_category')) {
                $categories = get_terms(array(
                    'taxonomy' => 'ai_category',
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'number' => 4,
                    'hide_empty' => false
                ));
                
                if (!empty($categories) && !is_wp_error($categories)) :
                    foreach ($categories as $category) :
                        $category_color = '#3a5cc9'; // Default color
                        $category_image = '';
                        
                        if (function_exists('get_field')) {
                            $cat_color = get_field('category_color', 'ai_category_' . $category->term_id);
                            if ($cat_color) {
                                $category_color = $cat_color;
                            }
                            
                            $cat_image = get_field('category_image', 'ai_category_' . $category->term_id);
                            if ($cat_image && isset($cat_image['url'])) {
                                $category_image = $cat_image['url'];
                            }
                        }
                        ?>
                        <div class="category-card modern-category-card">
                            <?php if ($category_image) : ?>
                                <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category->name); ?>" class="category-image">
                            <?php else: ?>
                                <div class="category-image-placeholder" style="background-color: <?php echo esc_attr($category_color); ?>;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>
                                </div>
                            <?php endif; ?>
                            
                            <div class="category-content">
                                <h4 class="category-title"><?php echo esc_html($category->name); ?></h4>
                                <p class="category-description"><?php echo esc_html($category->description); ?></p>
                                <a href="<?php echo get_term_link($category); ?>" class="category-btn" style="background-color: <?php echo esc_attr($category_color); ?>">
                                    <?php _e('Explore', 'inspiredshifter-child'); ?> <?php echo esc_html($category->name); ?>
                                </a>
                            </div>
                        </div>
                    <?php
                    endforeach;
                else:
                    ?>
                    <div class="no-categories-message">
                        <p><?php _e('No AI categories found. Create some categories to display them here.', 'inspiredshifter-child'); ?></p>
                    </div>
                    <?php
                endif;
            } else {
                ?>
                <div class="no-categories-message">
                    <p><?php _e('AI Categories taxonomy is not registered. Please check your theme setup.', 'inspiredshifter-child'); ?></p>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
</section>

<?php get_footer(); ?>