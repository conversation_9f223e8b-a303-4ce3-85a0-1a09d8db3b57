const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const { captureScreenshot } = require('./modules/puppeteer');
const { generateReview } = require('./modules/content');
const { publishToWordPress } = require('./modules/wordpress');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// API endpoint to scrape tool information
app.post('/api/scrape', async (req, res) => {
  const { toolName, toolUrl } = req.body;

  if (!toolName || !toolUrl) {
    return res.status(400).json({ success: false, error: 'Tool name and URL are required' });
  }

  try {
    const screenshotResult = await captureScreenshot(toolUrl);
    if (!screenshotResult.success) {
      return res.status(500).json({ success: false, error: screenshotResult.error });
    }

    return res.json({
      success: true,
      toolName,
      toolUrl,
      screenshot: screenshotResult.image
    });
  } catch (error) {
    console.error('Error scraping tool:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

// API endpoint to generate review content
app.post('/api/generate', async (req, res) => {
  const { toolName, toolUrl, toolData } = req.body;

  if (!toolName || !toolUrl) {
    return res.status(400).json({ success: false, error: 'Tool information is required' });
  }

  try {
    const content = await generateReview(toolName, toolUrl, toolData);
    return res.json({
      success: true,
      content
    });
  } catch (error) {
    console.error('Error generating review:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

// API endpoint to publish to WordPress
app.post('/api/publish', async (req, res) => {
  const { title, content } = req.body;

  if (!title || !content) {
    return res.status(400).json({ success: false, error: 'Title and content are required' });
  }

  try {
    const wpUsername = process.env.WP_USERNAME;
    const wpPassword = process.env.WP_PASSWORD;

    const result = await publishToWordPress(title, content, wpUsername, wpPassword);
    return res.json(result);
  } catch (error) {
    console.error('Error publishing to WordPress:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Access the application at http://localhost:${PORT}`);
});
