<?php
/**
 * AI InspiredShifter functions and definitions
 *
 * @package AI_InspiredShifter
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Define theme constants
 */
define('AI_INSPIREDSHIFTER_VERSION', '1.0.0');
define('AI_INSPIREDSHIFTER_DIR', get_template_directory());
define('AI_INSPIREDSHIFTER_URI', get_template_directory_uri());

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function ai_inspiredshifter_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embeds
    add_theme_support('responsive-embeds');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add support for full and wide align images
    add_theme_support('align-wide');

    // Add support for custom color palette - updated for dark cyberpunk neon theme
    add_theme_support('editor-color-palette', array(
        array(
            'name'  => __('Neon Orange', 'ai-inspiredshifter'),
            'slug'  => 'neon-orange',
            'color' => '#FFA500',
        ),
        array(
            'name'  => __('Neon Purple', 'ai-inspiredshifter'),
            'slug'  => 'neon-purple',
            'color' => '#BF00FF',
        ),
        array(
            'name'  => __('Neon Cyan', 'ai-inspiredshifter'),
            'slug'  => 'neon-cyan',
            'color' => '#00FFFF',
        ),
        array(
            'name'  => __('Dark Background', 'ai-inspiredshifter'),
            'slug'  => 'background-darkest',
            'color' => '#0a0a0a',
        ),
        array(
            'name'  => __('Dark Gray', 'ai-inspiredshifter'),
            'slug'  => 'background-darker',
            'color' => '#222222',
        ),
        array(
            'name'  => __('Light Gray', 'ai-inspiredshifter'),
            'slug'  => 'text-light',
            'color' => '#cccccc',
        ),
    ));

    // Register nav menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ai-inspiredshifter'),
        'footer'  => __('Footer Menu', 'ai-inspiredshifter'),
    ));
}
add_action('after_setup_theme', 'ai_inspiredshifter_setup');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 */
function ai_inspiredshifter_content_width() {
    $GLOBALS['content_width'] = apply_filters('ai_inspiredshifter_content_width', 1200);
}
add_action('after_setup_theme', 'ai_inspiredshifter_content_width', 0);

/**
 * Register widget areas.
 */
function ai_inspiredshifter_widgets_init() {
    register_sidebar(array(
        'name'          => __('Blog Sidebar', 'ai-inspiredshifter'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your blog sidebar.', 'ai-inspiredshifter'),
        'before_widget' => '<section id="%1$s" class="widget %2$s neon-widget">',
        'after_widget'  => '</section>',
        'before_title'  => '<h4 class="widget-title neon-widget-title">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 1', 'ai-inspiredshifter'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'ai-inspiredshifter'),
        'before_widget' => '<div id="%1$s" class="footer-widget neon-footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="neon-widget-title">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 2', 'ai-inspiredshifter'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'ai-inspiredshifter'),
        'before_widget' => '<div id="%1$s" class="footer-widget neon-footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="neon-widget-title">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 3', 'ai-inspiredshifter'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'ai-inspiredshifter'),
        'before_widget' => '<div id="%1$s" class="footer-widget neon-footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="neon-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'ai_inspiredshifter_widgets_init');

/**
 * Enqueue scripts and styles.
 */
function ai_inspiredshifter_scripts() {
    // Enqueue Google Fonts - Orbitron and Roboto for cyberpunk neon theme
    wp_enqueue_style('ai-inspiredshifter-fonts', 'https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap', array(), null);
    
    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    
    // Enqueue main stylesheet
    wp_enqueue_style('ai-inspiredshifter-style', get_stylesheet_uri(), array(), AI_INSPIREDSHIFTER_VERSION);
    
    // Enqueue custom styles
    wp_enqueue_style('ai-inspiredshifter-custom', AI_INSPIREDSHIFTER_URI . '/assets/css/custom.css', array(), AI_INSPIREDSHIFTER_VERSION);
    
    // Enqueue particles.js for hero particle effect
    wp_enqueue_script('particles-js', 'https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js', array(), '2.0.0', true);
    
    // Enqueue main JavaScript
    wp_enqueue_script('ai-inspiredshifter-main', AI_INSPIREDSHIFTER_URI . '/assets/js/main.js', array('jquery', 'particles-js'), AI_INSPIREDSHIFTER_VERSION, true);
    
    // Localize script for dynamic values
    wp_localize_script('ai-inspiredshifter-main', 'aiInspiredShifterVars', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'themeUri' => AI_INSPIREDSHIFTER_URI,
        'neonColors' => array(
            'orange' => '#FFA500',
            'purple' => '#BF00FF',
            'cyan' => '#00FFFF',
        )
    ));

    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'ai_inspiredshifter_scripts');

/**
 * Register Custom Post Type for AI Tools
 */
function ai_inspiredshifter_register_post_types() {
    $labels = array(
        'name'                  => _x('AI Tools', 'Post type general name', 'ai-inspiredshifter'),
        'singular_name'         => _x('AI Tool', 'Post type singular name', 'ai-inspiredshifter'),
        'menu_name'             => _x('AI Tools', 'Admin Menu text', 'ai-inspiredshifter'),
        'name_admin_bar'        => _x('AI Tool', 'Add New on Toolbar', 'ai-inspiredshifter'),
        'add_new'               => __('Add New', 'ai-inspiredshifter'),
        'add_new_item'          => __('Add New AI Tool', 'ai-inspiredshifter'),
        'new_item'              => __('New AI Tool', 'ai-inspiredshifter'),
        'edit_item'             => __('Edit AI Tool', 'ai-inspiredshifter'),
        'view_item'             => __('View AI Tool', 'ai-inspiredshifter'),
        'all_items'             => __('All AI Tools', 'ai-inspiredshifter'),
        'search_items'          => __('Search AI Tools', 'ai-inspiredshifter'),
        'parent_item_colon'     => __('Parent AI Tools:', 'ai-inspiredshifter'),
        'not_found'             => __('No AI tools found.', 'ai-inspiredshifter'),
        'not_found_in_trash'    => __('No AI tools found in Trash.', 'ai-inspiredshifter'),
        'featured_image'        => _x('AI Tool Logo', 'Overrides the "Featured Image" phrase', 'ai-inspiredshifter'),
        'set_featured_image'    => _x('Set tool logo', 'Overrides the "Set featured image" phrase', 'ai-inspiredshifter'),
        'remove_featured_image' => _x('Remove tool logo', 'Overrides the "Remove featured image" phrase', 'ai-inspiredshifter'),
        'use_featured_image'    => _x('Use as tool logo', 'Overrides the "Use as featured image" phrase', 'ai-inspiredshifter'),
        'archives'              => _x('AI Tool archives', 'The post type archive label used in nav menus', 'ai-inspiredshifter'),
        'insert_into_item'      => _x('Insert into AI tool', 'Overrides the "Insert into post" phrase', 'ai-inspiredshifter'),
        'uploaded_to_this_item' => _x('Uploaded to this AI tool', 'Overrides the "Uploaded to this post" phrase', 'ai-inspiredshifter'),
        'filter_items_list'     => _x('Filter AI tools list', 'Screen reader text for the filter links heading on the post type listing screen', 'ai-inspiredshifter'),
        'items_list_navigation' => _x('AI Tools list navigation', 'Screen reader text for the pagination heading on the post type listing screen', 'ai-inspiredshifter'),
        'items_list'            => _x('AI Tools list', 'Screen reader text for the items list heading on the post type listing screen', 'ai-inspiredshifter'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'ai-tool'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-superhero',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields', 'comments'),
        'show_in_rest'       => true,
    );

    register_post_type('ai_tool', $args);
}
add_action('init', 'ai_inspiredshifter_register_post_types');

/**
 * Register Custom Taxonomies for AI Tools
 */
function ai_inspiredshifter_register_taxonomies() {
    // AI Tool Category Taxonomy
    $category_labels = array(
        'name'                       => _x('AI Categories', 'taxonomy general name', 'ai-inspiredshifter'),
        'singular_name'              => _x('AI Category', 'taxonomy singular name', 'ai-inspiredshifter'),
        'search_items'               => __('Search AI Categories', 'ai-inspiredshifter'),
        'popular_items'              => __('Popular AI Categories', 'ai-inspiredshifter'),
        'all_items'                  => __('All AI Categories', 'ai-inspiredshifter'),
        'parent_item'                => __('Parent AI Category', 'ai-inspiredshifter'),
        'parent_item_colon'          => __('Parent AI Category:', 'ai-inspiredshifter'),
        'edit_item'                  => __('Edit AI Category', 'ai-inspiredshifter'),
        'update_item'                => __('Update AI Category', 'ai-inspiredshifter'),
        'add_new_item'               => __('Add New AI Category', 'ai-inspiredshifter'),
        'new_item_name'              => __('New AI Category Name', 'ai-inspiredshifter'),
        'separate_items_with_commas' => __('Separate AI categories with commas', 'ai-inspiredshifter'),
        'add_or_remove_items'        => __('Add or remove AI categories', 'ai-inspiredshifter'),
        'choose_from_most_used'      => __('Choose from the most used AI categories', 'ai-inspiredshifter'),
        'not_found'                  => __('No AI categories found.', 'ai-inspiredshifter'),
        'menu_name'                  => __('AI Categories', 'ai-inspiredshifter'),
    );

    $category_args = array(
        'hierarchical'      => true,
        'labels'            => $category_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ai-category'),
        'show_in_rest'      => true,
    );

    register_taxonomy('ai_category', array('ai_tool'), $category_args);

    // AI Tool Feature Taxonomy
    $feature_labels = array(
        'name'                       => _x('AI Features', 'taxonomy general name', 'ai-inspiredshifter'),
        'singular_name'              => _x('AI Feature', 'taxonomy singular name', 'ai-inspiredshifter'),
        'search_items'               => __('Search AI Features', 'ai-inspiredshifter'),
        'popular_items'              => __('Popular AI Features', 'ai-inspiredshifter'),
        'all_items'                  => __('All AI Features', 'ai-inspiredshifter'),
        'parent_item'                => null,
        'parent_item_colon'          => null,
        'edit_item'                  => __('Edit AI Feature', 'ai-inspiredshifter'),
        'update_item'                => __('Update AI Feature', 'ai-inspiredshifter'),
        'add_new_item'               => __('Add New AI Feature', 'ai-inspiredshifter'),
        'new_item_name'              => __('New AI Feature Name', 'ai-inspiredshifter'),
        'separate_items_with_commas' => __('Separate AI features with commas', 'ai-inspiredshifter'),
        'add_or_remove_items'        => __('Add or remove AI features', 'ai-inspiredshifter'),
        'choose_from_most_used'      => __('Choose from the most used AI features', 'ai-inspiredshifter'),
        'not_found'                  => __('No AI features found.', 'ai-inspiredshifter'),
        'menu_name'                  => __('AI Features', 'ai-inspiredshifter'),
    );

    $feature_args = array(
        'hierarchical'      => false,
        'labels'            => $feature_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ai-feature'),
        'show_in_rest'      => true,
    );

    register_taxonomy('ai_feature', array('ai_tool'), $feature_args);

    // AI Tool Pricing Taxonomy
    $pricing_labels = array(
        'name'                       => _x('Pricing Models', 'taxonomy general name', 'ai-inspiredshifter'),
        'singular_name'              => _x('Pricing Model', 'taxonomy singular name', 'ai-inspiredshifter'),
        'search_items'               => __('Search Pricing Models', 'ai-inspiredshifter'),
        'popular_items'              => __('Popular Pricing Models', 'ai-inspiredshifter'),
        'all_items'                  => __('All Pricing Models', 'ai-inspiredshifter'),
        'parent_item'                => null,
        'parent_item_colon'          => null,
        'edit_item'                  => __('Edit Pricing Model', 'ai-inspiredshifter'),
        'update_item'                => __('Update Pricing Model', 'ai-inspiredshifter'),
        'add_new_item'               => __('Add New Pricing Model', 'ai-inspiredshifter'),
        'new_item_name'              => __('New Pricing Model Name', 'ai-inspiredshifter'),
        'separate_items_with_commas' => __('Separate pricing models with commas', 'ai-inspiredshifter'),
        'add_or_remove_items'        => __('Add or remove pricing models', 'ai-inspiredshifter'),
        'choose_from_most_used'      => __('Choose from the most used pricing models', 'ai-inspiredshifter'),
        'not_found'                  => __('No pricing models found.', 'ai-inspiredshifter'),
        'menu_name'                  => __('Pricing Models', 'ai-inspiredshifter'),
    );

    $pricing_args = array(
        'hierarchical'      => false,
        'labels'            => $pricing_labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'pricing-model'),
        'show_in_rest'      => true,
    );

    register_taxonomy('pricing_model', array('ai_tool'), $pricing_args);
}
add_action('init', 'ai_inspiredshifter_register_taxonomies');

/**
 * Add Elementor support
 */
function ai_inspiredshifter_add_elementor_support() {
    // Add support for Elementor Pro locations
    if (function_exists('elementor_theme_do_location')) {
        add_theme_support('elementor-pro');
    }
}
add_action('after_setup_theme', 'ai_inspiredshifter_add_elementor_support');

/**
 * Include required files
 */
require_once AI_INSPIREDSHIFTER_DIR . '/inc/template-functions.php';
require_once AI_INSPIREDSHIFTER_DIR . '/inc/template-tags.php';
require_once AI_INSPIREDSHIFTER_DIR . '/inc/customizer.php';

/**
 * Add ACF fields if ACF is active
 */
function ai_inspiredshifter_acf_init() {
    if (function_exists('acf_add_local_field_group')) {
        require_once AI_INSPIREDSHIFTER_DIR . '/inc/acf-fields.php';
    }
}
add_action('acf/init', 'ai_inspiredshifter_acf_init');

/**
 * Add social sharing functionality
 */
function ai_inspiredshifter_social_sharing() {
    global $post;
    
    // Get current page URL
    $url = urlencode(get_permalink());
    
    // Get current page title
    $title = urlencode(get_the_title());
    
    // Get post thumbnail for Pinterest
    $thumbnail = '';
    if (has_post_thumbnail($post->ID)) {
        $thumbnail = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'full');
        $thumbnail = $thumbnail[0];
    }
    
    // Construct sharing URLs
    $twitter_url = 'https://twitter.com/intent/tweet?text=' . $title . '&url=' . $url;
    $facebook_url = 'https://www.facebook.com/sharer/sharer.php?u=' . $url;
    $linkedin_url = 'https://www.linkedin.com/shareArticle?mini=true&url=' . $url . '&title=' . $title;
    $pinterest_url = 'https://pinterest.com/pin/create/button/?url=' . $url . '&media=' . $thumbnail . '&description=' . $title;
    
    // Output sharing buttons with neon styling
    $html = '<div class="social-sharing neon-social-sharing">';
    $html .= '<span class="share-title neon-share-title">' . __('Share:', 'ai-inspiredshifter') . '</span>';
    $html .= '<a class="share-twitter neon-social-link" href="' . $twitter_url . '" target="_blank" rel="nofollow"><i class="fab fa-twitter neon-icon-glow"></i></a>';
    $html .= '<a class="share-facebook neon-social-link" href="' . $facebook_url . '" target="_blank" rel="nofollow"><i class="fab fa-facebook-f neon-icon-glow"></i></a>';
    $html .= '<a class="share-linkedin neon-social-link" href="' . $linkedin_url . '" target="_blank" rel="nofollow"><i class="fab fa-linkedin-in neon-icon-glow"></i></a>';
    $html .= '<a class="share-pinterest neon-social-link" href="' . $pinterest_url . '" target="_blank" rel="nofollow"><i class="fab fa-pinterest-p neon-icon-glow"></i></a>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Add a custom shortcode for social sharing
 */
function ai_inspiredshifter_social_sharing_shortcode() {
    return ai_inspiredshifter_social_sharing();
}
add_shortcode('social_sharing', 'ai_inspiredshifter_social_sharing_shortcode');

/**
 * Add a custom shortcode for affiliate links
 */
function ai_inspiredshifter_affiliate_link_shortcode($atts) {
    $atts = shortcode_atts(array(
        'url' => '',
        'text' => __('Try Now', 'ai-inspiredshifter'),
        'class' => 'button',
    ), $atts);
    
    if (empty($atts['url'])) {
        return '';
    }
    
    $class = !empty($atts['class']) ? ' class="' . esc_attr($atts['class']) . '"' : '';
    
    return '<a href="' . esc_url($atts['url']) . '"' . $class . ' target="_blank" rel="nofollow noopener">' . esc_html($atts['text']) . '</a>';
}
add_shortcode('affiliate_link', 'ai_inspiredshifter_affiliate_link_shortcode');

/**
 * Add a custom shortcode for rating display
 */
function ai_inspiredshifter_rating_shortcode($atts) {
    $atts = shortcode_atts(array(
        'rating' => 0,
        'max' => 5,
        'class' => '',
    ), $atts);
    
    $rating = floatval($atts['rating']);
    $max = intval($atts['max']);
    $class = $atts['class'] ? ' ' . esc_attr($atts['class']) : '';
    
    if ($rating <= 0 || $max <= 0) {
        return '';
    }
    
    $html = '<div class="star-rating' . $class . '">';
    
    // Full stars
    $full_stars = floor($rating);
    for ($i = 0; $i < $full_stars; $i++) {
        $html .= '<i class="fas fa-star neon-icon-glow"></i>';
    }
    
    // Half star
    $half_star = $rating - $full_stars;
    if ($half_star >= 0.25 && $half_star < 0.75) {
        $html .= '<i class="fas fa-star-half-alt neon-icon-glow"></i>';
        $empty_stars = $max - $full_stars - 1;
    } elseif ($half_star >= 0.75) {
        $html .= '<i class="fas fa-star neon-icon-glow"></i>';
        $empty_stars = $max - $full_stars - 1;
    } else {
        $empty_stars = $max - $full_stars;
    }
    
    // Empty stars
    for ($i = 0; $i < $empty_stars; $i++) {
        $html .= '<i class="far fa-star neon-icon-glow"></i>';
    }
    
    $html .= '<span class="rating-value neon-rating-value">' . number_format($rating, 1) . '/' . $max . '</span>';
    $html .= '</div>';
    
    return $html;
}
add_shortcode('rating', 'ai_inspiredshifter_rating_shortcode');

/**
 * Add a back to top button
 */
function ai_inspiredshifter_back_to_top() {
    echo '<a href="#" id="back-to-top" class="back-to-top neon-back-to-top"><i class="fas fa-arrow-up neon-icon-glow"></i></a>';
}
add_action('wp_footer', 'ai_inspiredshifter_back_to_top');

/**
 * Add a loading animation
 */
function ai_inspiredshifter_loading_animation() {
    echo '<div id="page-loader" class="page-loader neon-loader">
        <div class="loader-inner">
            <div class="loader-logo neon-loader-logo">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48">
                    <path fill="url(#gradient)" d="M13 9h5.5L13 3.5V9M6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4c0-1.11.89-2 2-2m5 2v5h2V4h-2m-5 12h10v-2H6v2z"/>
                    <defs>
                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#FFA500" />
                            <stop offset="50%" stop-color="#BF00FF" />
                            <stop offset="100%" stop-color="#00FFFF" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
            <div class="loader-spinner neon-loader-spinner">
                <div class="spinner-dot neon-dot"></div>
                <div class="spinner-dot neon-dot"></div>
                <div class="spinner-dot neon-dot"></div>
            </div>
        </div>
    </div>';
}
add_action('wp_body_open', 'ai_inspiredshifter_loading_animation');


/**
 * AJAX handler for blog posts filtering by category
 */
function ai_inspiredshifter_ajax_filter_blog() {
    $category_slug = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : '';

    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 10,
        'orderby' => 'date',
        'order' => 'DESC',
    );

    if ($category_slug) {
        $args['category_name'] = $category_slug;
    }

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('template-parts/content', get_post_type());
        }
        wp_send_json_success(ob_get_clean());
    } else {
        wp_send_json_error('No posts found.');
    }
    wp_die();
}
add_action('wp_ajax_filter_blog', 'ai_inspiredshifter_ajax_filter_blog');
add_action('wp_ajax_nopriv_filter_blog', 'ai_inspiredshifter_ajax_filter_blog');

/**
 * AJAX handler for AI Tools filtering and sorting
 */
function ai_inspiredshifter_ajax_filter_ai_tools() {
    $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : '';
    $pricing = isset($_POST['pricing']) ? sanitize_text_field($_POST['pricing']) : '';
    $feature = isset($_POST['feature']) ? sanitize_text_field($_POST['feature']) : '';
    $sort = isset($_POST['sort']) ? sanitize_text_field($_POST['sort']) : 'date';

    $tax_query = array('relation' => 'AND');

    if ($category) {
        $tax_query[] = array(
            'taxonomy' => 'ai_category',
            'field' => 'slug',
            'terms' => $category,
        );
    }
    if ($pricing) {
        $tax_query[] = array(
            'taxonomy' => 'pricing_model',
            'field' => 'slug',
            'terms' => $pricing,
        );
    }
    if ($feature) {
        $tax_query[] = array(
            'taxonomy' => 'ai_feature',
            'field' => 'slug',
            'terms' => $feature,
        );
    }

    $orderby = 'date';
    $order = 'DESC';

    if ($sort === 'title') {
        $orderby = 'title';
        $order = 'ASC';
    } elseif ($sort === 'rating') {
        $orderby = 'meta_value_num';
        $order = 'DESC';
    }

    $meta_query = array();

    if ($sort === 'rating') {
        $meta_query[] = array(
            'key' => 'tool_rating',
            'compare' => 'EXISTS',
            'type' => 'NUMERIC',
        );
    }

    $args = array(
        'post_type' => 'ai_tool',
        'posts_per_page' => 10,
        'orderby' => $orderby,
        'order' => $order,
        'tax_query' => $tax_query,
        'meta_query' => $meta_query,
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();

            // Prepare category classes for styling
            $categories = get_the_terms(get_the_ID(), 'ai_category');
            $category_classes = '';
            if (!empty($categories) && !is_wp_error($categories)) {
                foreach ($categories as $cat) {
                    $category_classes .= ' category-' . $cat->slug;
                }
            }

            ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('tool-card neon-tool-card' . esc_attr($category_classes)); ?>>
                <header class="entry-header neon-entry-header">
                    <h2 class="entry-title neon-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                </header>
                <div class="entry-summary neon-entry-summary">
                    <?php the_excerpt(); ?>
                </div>
            </article>
            <?php
        }
        wp_send_json_success(ob_get_clean());
    } else {
        wp_send_json_error('No AI tools found.');
    }
    wp_die();
}
add_action('wp_ajax_filter_ai_tools', 'ai_inspiredshifter_ajax_filter_ai_tools');
add_action('wp_ajax_nopriv_filter_ai_tools', 'ai_inspiredshifter_ajax_filter_ai_tools');
