/*
Theme Name: AI InspiredShifter
Theme URI: https://inspiredshifter.com
Description: A modern WordPress theme focused on artificial intelligence tools, resources, and insights with dark cyberpunk neon design
Version: 1.0.0
Author: InspiredShifter
Author URI: https://inspiredshifter.com
Text Domain: ai-inspiredshifter
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: custom-background, custom-logo, custom-menu, featured-images, threaded-comments, translation-ready
*/

/* ======================================
   VARIABLES - Cyberpunk Neon Theme
====================================== */
:root {
    /* Dark Cyberpunk Colors */
    --background-darkest: #0a0a0a;
    --background-darker: #222222;
    --text-light: #cccccc;
    
    /* Neon Colors */
    --neon-orange: #FFA500;
    --neon-purple: #BF00FF;
    --neon-cyan: #00FFFF;
    
    /* Base Colors */
    --background-light: var(--background-darkest);
    --background-white: var(--background-darker);
    --text-dark: var(--neon-orange);
    --text-body: var(--text-light);
    --text-muted: #666666;
    
    /* Typography */
    --heading-font: 'Orbitron', sans-serif;
    --body-font: 'Roboto', sans-serif;
    
    /* Spacing */
    --section-padding: 80px;
    --section-padding-mobile: 60px;
    --container-width: 1200px;
    
    /* UI Elements */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --box-shadow: 0 0 15px rgba(255, 165, 0, 0.4);
    --box-shadow-hover: 0 0 30px rgba(191, 0, 255, 0.6);
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    
    /* Neon Glow Effects */
    --glow-orange: 0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange);
    --glow-purple: 0 0 5px var(--neon-purple), 0 0 10px var(--neon-purple);
    --glow-cyan: 0 0 5px var(--neon-cyan), 0 0 10px var(--neon-cyan);
    --glow-text: 0 0 5px currentColor;
}

/* ======================================
   GLOBAL STYLES - Dark Cyberpunk Neon Theme
====================================== */
body {
    font-family: var(--body-font);
    color: var(--text-body);
    line-height: 1.6;
    background-color: var(--background-light);
    overflow-x: hidden;
    transition: background-color 0.5s ease, color 0.5s ease;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    color: var(--neon-orange);
    line-height: 1.3;
    margin-top: 0;
    font-weight: 700;
    text-shadow:
        0 0 5px var(--neon-orange),
        0 0 10px var(--neon-orange),
        0 0 20px var(--neon-purple);
}

h1 {
    font-size: 46px;
    margin-bottom: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

h2 {
    font-size: 36px;
    margin-bottom: 15px;
    letter-spacing: 0.5px;
}

h3 {
    font-size: 28px;
    margin-bottom: 12px;
    font-weight: 600;
}

h4 {
    font-size: 22px;
    margin-bottom: 10px;
    font-weight: 600;
}

h5 {
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 600;
}

h6 {
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

p {
    margin-bottom: 1.5rem;
    font-size: 16px;
    color: var(--text-body);
}

a {
    color: var(--neon-cyan);
    text-decoration: none;
    transition: var(--transition-fast);
    text-shadow: var(--glow-cyan);
    position: relative;
    z-index: 0;
}

a::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--neon-cyan);
    opacity: 0.3;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
    z-index: -1;
}

a:hover::before {
    width: 120%;
    height: 120%;
}

a:hover {
    color: var(--neon-purple);
    text-shadow:
        0 0 8px var(--neon-purple),
        0 0 15px var(--neon-cyan);
    transform: scale(1.05);
    z-index: 1;
}

img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Button Styles */
.button,
button,
input[type="button"],
input[type="submit"] {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--neon-orange), var(--neon-purple));
    border: 2px solid transparent;
    color: var(--background-darkest);
    border-radius: var(--border-radius-sm);
    font-family: var(--heading-font);
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition-fast);
    text-align: center;
    line-height: 1.4;
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 0;
    overflow: hidden;
    letter-spacing: 1px;
}

.button::before,
button::before,
input[type="button"]::before,
input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.button:hover::before,
button:hover::before,
input[type="button"]:hover::before,
input[type="submit"]:hover::before {
    left: 100%;
}

.button:hover,
button:hover,
input[type="button"]:hover,
input[type="submit"]:hover {
    background: linear-gradient(135deg, var(--neon-purple), var(--neon-cyan));
    color: white;
    box-shadow:
        0 0 10px var(--neon-purple),
        0 0 20px var(--neon-cyan);
    transform: translateY(-2px) scale(1.03);
    z-index: 1;
}

/* Secondary Button */
.button-secondary {
    background: transparent;
    color: var(--neon-orange);
    border: 2px solid var(--neon-orange);
    box-shadow:
        0 0 5px var(--neon-orange),
        inset 0 0 5px var(--neon-orange);
    text-shadow: var(--glow-orange);
}

.button-secondary:hover {
    background: var(--neon-orange);
    color: var(--background-darkest);
    box-shadow:
        0 0 10px var(--neon-orange),
        0 0 20px var(--neon-purple),
        inset 0 0 10px var(--background-darkest);
    text-shadow: none;
}

/* Neon Theme Elements */
.neon-theme {
    background-color: var(--background-darkest);
    color: var(--text-body);
}

.neon-container {
    position: relative;
}

.neon-title {
    color: var(--neon-orange);
    text-shadow:
        0 0 5px var(--neon-orange),
        0 0 10px var(--neon-orange),
        0 0 20px var(--neon-purple);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.neon-subtitle {
    color: var(--neon-cyan);
    text-shadow: var(--glow-cyan);
}

.neon-text {
    color: var(--text-light);
}

.neon-icon {
    color: var(--neon-orange);
    margin-right: 5px;
}

.neon-icon-glow {
    color: var(--neon-orange);
    text-shadow: var(--glow-orange);
    transition: var(--transition-fast);
}

.neon-icon-glow:hover {
    color: var(--neon-purple);
    text-shadow: var(--glow-purple);
}

.neon-glow {
    box-shadow:
        0 0 5px var(--neon-orange),
        0 0 10px var(--neon-orange);
    transition: var(--transition-fast);
}

.neon-glow:hover {
    box-shadow:
        0 0 10px var(--neon-purple),
        0 0 15px var(--neon-cyan);
}

/* Category Buttons */
.category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 24px;
}

.category-button,
.neon-category-button {
    background: transparent;
    border: 2px solid var(--neon-orange);
    color: var(--neon-orange);
    padding: 8px 16px;
    border-radius: var(--border-radius-md);
    font-family: var(--heading-font);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    user-select: none;
    text-shadow: var(--glow-orange);
    box-shadow: 0 0 5px var(--neon-orange);
    letter-spacing: 1px;
}

.category-button:hover,
.category-button.active,
.neon-category-button:hover,
.neon-category-button.active {
    background: linear-gradient(90deg, var(--neon-orange), var(--neon-purple));
    color: var(--background-darkest);
    box-shadow:
        0 0 8px var(--neon-orange),
        0 0 16px var(--neon-purple);
    transform: translateY(-2px) scale(1.05);
    text-shadow: none;
}

/* Blog Grid */
.blog-grid,
.neon-blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}

/* Tools Filter Container */
.tools-filter-container,
.neon-tools-filter-container {
    margin-bottom: 30px;
    padding: 20px;
    border-radius: var(--border-radius-lg);
    background-color: rgba(34, 34, 34, 0.8);
    border: 1px solid var(--neon-orange);
    box-shadow: 
        0 0 10px var(--neon-orange),
        inset 0 0 5px rgba(255, 165, 0, 0.3);
}

.tools-filters,
.neon-tools-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.filter-group,
.neon-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-label,
.neon-filter-label {
    font-family: var(--heading-font);
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--neon-orange);
    text-shadow: var(--glow-orange);
    letter-spacing: 1px;
}

.filter-select,
.neon-filter-select {
    padding: 10px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--neon-orange);
    background-color: rgba(10, 10, 10, 0.8);
    color: var(--neon-cyan);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    text-shadow: var(--glow-cyan);
    box-shadow: 0 0 5px var(--neon-orange);
}

.filter-select:hover,
.filter-select:focus,
.neon-filter-select:hover,
.neon-filter-select:focus {
    border-color: var(--neon-purple);
    box-shadow:
        0 0 8px var(--neon-purple),
        0 0 16px var(--neon-cyan);
    outline: none;
}

/* Tools Grid */
.tools-grid,
.neon-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}

/* Tool Card */
.tool-card,
.neon-tool-card {
    background-color: rgba(34, 34, 34, 0.8);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(191, 0, 255, 0.3);
    box-shadow: 
        0 0 10px rgba(191, 0, 255, 0.3),
        inset 0 0 5px rgba(0, 255, 255, 0.1);
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    z-index: 0;
    overflow: hidden;
}

.tool-card::after,
.neon-tool-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(0, 255, 255, 0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
}

.tool-card:hover,
.neon-tool-card:hover {
    box-shadow:
        0 0 15px var(--neon-purple),
        0 0 30px var(--neon-cyan),
        inset 0 0 10px rgba(0, 255, 255, 0.2);
    transform: translateY(-5px);
    border-color: var(--neon-purple);
    z-index: 1;
}

.tool-card:hover::after,
.neon-tool-card:hover::after {
    opacity: 1;
}

/* Header & Navigation */
.site-header,
.neon-header {
    background-color: rgba(10, 10, 10, 0.95);
    border-bottom: 1px solid var(--neon-orange);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition-fast);
}

.header-inner,
.neon-header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.site-branding,
.neon-branding {
    display: flex;
    align-items: center;
}

.site-logo,
.neon-logo {
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 28px;
}

.site-logo a,
.neon-logo a,
.neon-site-title {
    color: var(--neon-orange);
    text-decoration: none;
    display: flex;
    align-items: center;
    text-shadow:
        0 0 5px var(--neon-orange),
        0 0 10px var(--neon-orange),
        0 0 20px var(--neon-purple);
    letter-spacing: 1px;
}

/* Main Navigation */
.main-navigation,
.neon-navigation {
    font-family: var(--heading-font);
}

.main-navigation ul,
.neon-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    margin-left: 30px;
    position: relative;
}

.main-navigation a,
.neon-menu-link {
    color: var(--text-light);
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: var(--transition-fast);
    padding: 8px 0;
    position: relative;
    letter-spacing: 1px;
}

.main-navigation a:hover,
.neon-menu-link:hover {
    color: var(--neon-cyan);
    text-shadow:
        0 0 5px var(--neon-cyan),
        0 0 10px var(--neon-cyan);
}

.main-navigation a::after,
.neon-menu-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-orange), var(--neon-purple));
    transition: var(--transition-fast);
}

.main-navigation a:hover::after,
.neon-menu-link:hover::after {
    width: 100%;
    box-shadow: 0 0 8px var(--neon-orange);
}

/* Theme Toggle Button */
.theme-toggle-button,
.neon-toggle-button {
    background: none;
    border: none;
    color: var(--neon-cyan);
    font-size: 20px;
    cursor: pointer;
    margin-left: 20px;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition-fast);
    text-shadow: var(--glow-cyan);
}

.theme-toggle-button:hover,
.neon-toggle-button:hover {
    color: var(--neon-purple);
    text-shadow: var(--glow-purple);
    transform: rotate(30deg) scale(1.2);
}

/* Mobile Menu Toggle */
.menu-toggle,
.neon-menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    width: 30px;
    height: 20px;
    position: relative;
    z-index: 101;
}

.menu-toggle span,
.neon-menu-toggle span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--neon-orange);
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: var(--transition-fast);
    box-shadow: var(--glow-orange);
}

/* Footer Styling */
.site-footer,
.neon-footer {
    background-color: rgba(10, 10, 10, 0.95);
    border-top: 1px solid var(--neon-orange);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
    padding: 60px 0 20px;
    color: var(--text-body);
}

.footer-widgets,
.neon-footer-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-widget-column,
.neon-footer-column {
    margin-bottom: 30px;
}

.footer-widget h4,
.neon-widget-title {
    color: var(--neon-orange);
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
    text-shadow: var(--glow-orange);
    letter-spacing: 1px;
}

.footer-widget h4::after,
.neon-widget-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-orange), var(--neon-purple));
    box-shadow: var(--glow-orange);
}

.footer-widget ul,
.neon-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.footer-widget ul li,
.neon-list li {
    margin-bottom: 10px;
}

.footer-widget ul li a,
.neon-link {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
    display: inline-block;
}

.footer-widget ul li a:hover,
.neon-link:hover {
    color: var(--neon-cyan);
    transform: translateX(5px);
    text-shadow: var(--glow-cyan);
}

.social-icons,
.neon-social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icons a,
.neon-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    border: 1px solid var(--neon-orange);
    color: var(--neon-orange);
    text-decoration: none;
    transition: var(--transition-fast);
    font-size: 18px;
    box-shadow: 0 0 5px var(--neon-orange);
}

.social-icons a:hover,
.neon-social-link:hover {
    background: var(--neon-orange);
    color: var(--background-darkest);
    border-color: var(--neon-orange);
    box-shadow: 0 0 15px var(--neon-orange);
    transform: translateY(-3px);
}

.footer-subscribe,
.neon-subscribe-form {
    display: flex;
    margin-top: 20px;
    max-width: 320px;
}

.footer-subscribe input,
.neon-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--neon-orange);
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    background-color: rgba(10, 10, 10, 0.8);
    color: var(--text-body);
    font-family: var(--body-font);
    box-shadow: inset 0 0 5px rgba(255, 165, 0, 0.3);
}

.footer-subscribe input:focus,
.neon-input:focus {
    outline: none;
    border-color: var(--neon-purple);
    box-shadow: inset 0 0 8px rgba(191, 0, 255, 0.5);
}

.footer-subscribe button,
.neon-subscribe-form .neon-button {
    padding: 0 20px;
    border: none;
    background: linear-gradient(135deg, var(--neon-orange), var(--neon-purple));
    color: white;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--heading-font);
    font-weight: 600;
}

.footer-subscribe button:hover,
.neon-subscribe-form .neon-button:hover {
    background: linear-gradient(135deg, var(--neon-purple), var(--neon-cyan));
    box-shadow: 0 0 15px var(--neon-purple);
}

.footer-bottom,
.neon-footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 165, 0, 0.2);
}

/* Sidebar Styling */
.widget-area,
.neon-sidebar {
    background-color: rgba(34, 34, 34, 0.8);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    border: 1px solid rgba(191, 0, 255, 0.3);
    box-shadow: 
        0 0 10px rgba(191, 0, 255, 0.3),
        inset 0 0 5px rgba(0, 255, 255, 0.1);
}

.widget {
    margin-bottom: 30px;
}

.widget:last-child {
    margin-bottom: 0;
}

.widget-title {
    color: var(--neon-orange);
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
    text-shadow: var(--glow-orange);
    letter-spacing: 1px;
    font-family: var(--heading-font);
}

.widget-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-orange), var(--neon-purple));
    box-shadow: var(--glow-orange);
}

/* Content Area Layout */
.content-area,
.neon-content-area {
    flex: 1;
}

/* Hero Section Styling */
.hero-section,
.neon-hero {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background-color: var(--background-darkest);
}

.hero-background,
.neon-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.floating-shape,
.neon-shape {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(255, 165, 0, 0.2), transparent 70%);
    filter: blur(60px);
    opacity: 0.5;
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 300px;
    height: 300px;
    left: 10%;
    top: 20%;
    background: radial-gradient(circle at center, rgba(255, 165, 0, 0.3), transparent 70%);
    animation-duration: 30s;
}

.shape-2 {
    width: 400px;
    height: 400px;
    right: -100px;
    top: -100px;
    background: radial-gradient(circle at center, rgba(191, 0, 255, 0.3), transparent 70%);
    animation-duration: 40s;
    animation-delay: 5s;
}

.shape-3 {
    width: 250px;
    height: 250px;
    left: 30%;
    bottom: -50px;
    background: radial-gradient(circle at center, rgba(0, 255, 255, 0.3), transparent 70%);
    animation-duration: 25s;
    animation-delay: 10s;
}

.shape-4 {
    width: 200px;
    height: 200px;
    right: 20%;
    bottom: 20%;
    background: radial-gradient(circle at center, rgba(255, 165, 0, 0.2), transparent 70%);
    animation-duration: 35s;
    animation-delay: 15s;
}

@keyframes float {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(-5%, 5%) rotate(5deg);
    }
    50% {
        transform: translate(5%, -5%) rotate(10deg);
    }
    75% {
        transform: translate(-5%, -5%) rotate(5deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

/* ======================================
   RESPONSIVE STYLES
====================================== */
@media (max-width: 992px) {
    .menu-toggle,
    .neon-menu-toggle {
        display: block;
    }
    
    .main-navigation,
    .neon-navigation {
        position: fixed;
        top: 0;
        right: -100%;
        width: 300px;
        height: 100vh;
        background: rgba(10, 10, 10, 0.95);
        box-shadow: -5px 0 15px rgba(255, 165, 0, 0.4);
        transition: var(--transition-medium);
        z-index: 100;
        padding: 80px 30px 30px;
        overflow-y: auto;
        border-left: 1px solid var(--neon-orange);
    }
    
    .main-navigation.active,
    .neon-navigation.active {
        right: 0;
    }
    
    .main-navigation ul,
    .neon-menu {
        flex-direction: column;
    }
    
    .main-navigation li {
        margin: 0 0 15px;
    }
    
    .main-navigation a,
    .neon-menu-link {
        display: block;
        padding: 10px 0;
        font-size: 18px;
    }
    
    .content-area,
    .neon-content-area,
    .widget-area,
    .neon-sidebar {
        width: 100%;
    }
    
    .widget-area,
    .neon-sidebar {
        margin-top: 40px;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 32px;
    }
    
    h2 {
        font-size: 28px;
    }
    
    h3 {
        font-size: 22px;
    }
    
    .section-padding {
        padding: var(--section-padding-mobile) 0;
    }
    
    .footer-widgets,
    .neon-footer-widgets {
        grid-template-columns: 1fr;
    }
    
    .hero-section,
    .neon-hero {
        padding: 60px 0;
    }
    
    .tools-filters,
    .neon-tools-filters {
        flex-direction: column;
    }
}

/* ======================================
   ANIMATIONS
====================================== */
@keyframes neonPulse {
    0% {
        text-shadow: 0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange);
    }
    50% {
        text-shadow: 0 0 15px var(--neon-orange), 0 0 30px var(--neon-purple);
    }
    100% {
        text-shadow: 0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange);
    }
}

@keyframes borderPulse {
    0% {
        box-shadow: 0 0 5px var(--neon-orange), inset 0 0 5px var(--neon-orange);
    }
    50% {
        box-shadow: 0 0 15px var(--neon-orange), 0 0 30px var(--neon-purple), inset 0 0 10px var(--neon-orange);
    }
    100% {
        box-shadow: 0 0 5px var(--neon-orange), inset 0 0 5px var(--neon-orange);
    }
}

.neon-pulse {
    animation: neonPulse 2s infinite;
}

.border-pulse {
    animation: borderPulse 2s infinite;
}

/* ======================================
   ELEMENTOR COMPATIBILITY
====================================== */
.elementor-section.elementor-section-boxed > .elementor-container {
    max-width: var(--container-width);
}

.elementor-widget-heading .elementor-heading-title {
    font-family: var(--heading-font);
}

.elementor-widget-text-editor {
    font-family: var(--body-font);
}

.elementor-button {
    background: linear-gradient(135deg, var(--neon-orange), var(--neon-purple));
    font-family: var(--heading-font);
    font-weight: 600;
    transition: var(--transition-fast);
}

.elementor-button:hover {
    background: linear-gradient(135deg, var(--neon-purple), var(--neon-cyan));
    box-shadow:
        0 0 10px var(--neon-purple),
        0 0 20px var(--neon-cyan);
    transform: translateY(-2px) scale(1.03);
}
