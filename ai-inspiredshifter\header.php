<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package AI_InspiredShifter
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>

<body <?php body_class('neon-theme dark-mode'); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site neon-site">
    <a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e('Skip to content', 'ai-inspiredshifter'); ?></a>

    <header id="masthead" class="site-header neon-header">
        <div class="container neon-container">
            <div class="header-inner neon-header-inner">
                <div class="site-branding neon-branding">
                    <?php
                    if (has_custom_logo()) :
                        the_custom_logo();
                    else :
                    ?>
                        <div class="site-logo neon-logo">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="neon-site-title">
                                <i class="fas fa-bolt neon-icon-glow"></i>
                                <?php bloginfo('name'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div><!-- .site-branding -->

                <nav id="site-navigation" class="main-navigation neon-navigation">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'menu_class'     => 'neon-menu',
                        'container'      => false,
                        'fallback_cb'    => function() {
                            echo '<ul id="primary-menu" class="menu neon-menu">';
                            echo '<li><a href="' . esc_url(admin_url('nav-menus.php')) . '" class="neon-menu-link">' . esc_html__('Add a menu', 'ai-inspiredshifter') . '</a></li>';
                            echo '</ul>';
                        },
                    ));
                    ?>
                </nav><!-- #site-navigation -->

                <!-- Dark/Light Mode Toggle Button -->
                <button id="theme-toggle" aria-label="Toggle Dark/Light Mode" class="theme-toggle-button neon-toggle-button">
                    <i class="fas fa-moon neon-icon-glow"></i>
                </button>

                <button class="menu-toggle neon-menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <span class="neon-glow"></span>
                    <span class="neon-glow"></span>
                    <span class="neon-glow"></span>
                    <span class="neon-glow"></span>
                </button>
            </div>
        </div>
    </header><!-- #masthead -->

    <div id="content" class="site-content neon-content">
