/**
 * Main JavaScript for AI InspiredShifter theme
 * Implements interactive features:
 * - Mobile hamburger menu with smooth animated transition
 * - Neon particles effect on homepage hero
 * - Back-to-top button with smooth scroll
 * - Hover effects and microinteractions for cards and buttons
 * - Smooth scroll for anchor links (e.g. table of contents)
 * - AJAX filtering for blog and AI tools with loading indicators
 */

(function () {
  'use strict';

  // Mobile Hamburger Menu
  const mobileMenu = (() => {
    const menuToggle = document.querySelector('.menu-toggle, .neon-menu-toggle');
    const menu = document.querySelector('.main-navigation, .neon-navigation');

    if (!menuToggle || !menu) return;

    const toggleMenu = () => {
      menuToggle.classList.toggle('active');
      menu.classList.toggle('active');
      // Animate menu sliding in/out
      if (menu.classList.contains('active')) {
        menu.style.maxHeight = menu.scrollHeight + 'px';
      } else {
        menu.style.maxHeight = null;
      }
    };

    menuToggle.addEventListener('click', toggleMenu);

    // Close menu on link click (mobile)
    menu.querySelectorAll('a').forEach(link => {
      link.addEventListener('click', () => {
        if (menu.classList.contains('active')) {
          toggleMenu();
        }
      });
    });
  })();

  // Particles Effect for Hero Section
  const heroParticles = (() => {
    const hero = document.querySelector('.hero-section, .neon-hero');
    if (!hero) return;

    // Create canvas for particles
    const canvas = document.createElement('canvas');
    canvas.className = 'particles-canvas';
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '1';
    
    hero.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // Resize canvas
    const resizeCanvas = () => {
      canvas.width = hero.offsetWidth;
      canvas.height = hero.offsetHeight;
    };
    
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
    
    // Particle class
    class Particle {
      constructor() {
        this.reset();
      }
      
      reset() {
        // Random position
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        
        // Random size
        this.size = Math.random() * 3 + 1;
        
        // Random velocity
        this.vx = Math.random() * 0.5 - 0.25;
        this.vy = Math.random() * 0.5 - 0.25;
        
        // Random color (neon purple/electric blue variations)
        this.color = Math.random() > 0.5 ? 
          `rgba(191, 0, 255, ${Math.random() * 0.6 + 0.2})` : 
          `rgba(0, 255, 255, ${Math.random() * 0.6 + 0.2})`;
        
        // Random lifetime
        this.life = 0;
        this.maxLife = Math.random() * 200 + 50;
      }
      
      update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life++;
        
        // Create glow effect by varying opacity
        const normalizedLife = this.life / this.maxLife;
        const opacity = normalizedLife < 0.5 ? 
          normalizedLife * 2 : 
          (1 - normalizedLife) * 2;
        
        // Reset if out of bounds or exceeded lifetime
        if (this.x < 0 || this.x > canvas.width || 
            this.y < 0 || this.y > canvas.height ||
            this.life > this.maxLife) {
          this.reset();
        }
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();
        
        // Add glow effect
        ctx.shadowBlur = 15;
        ctx.shadowColor = this.color;
      }
    }
    
    // Create particles
    const particles = [];
    const particleCount = Math.min(Math.floor(canvas.width * canvas.height / 10000), 100);
    
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle());
    }
    
    // Animation loop
    let animationFrameId;
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw connection lines between nearby particles
      ctx.strokeStyle = 'rgba(191, 0, 255, 0.1)';
      ctx.lineWidth = 0.5;
      
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.globalAlpha = 1 - (distance / 100);
            ctx.stroke();
            ctx.globalAlpha = 1;
          }
        }
      }
      
      // Update particles
      particles.forEach(particle => particle.update());
      
      animationFrameId = requestAnimationFrame(animate);
    };
    
    animationFrameId = requestAnimationFrame(animate);
    
    // Cleanup on page unload
    window.addEventListener('unload', () => {
      cancelAnimationFrame(animationFrameId);
    });
  })();

  // Back-to-top Button with Smooth Scroll
  const backToTop = (() => {
    const btn = document.createElement('button');
    btn.className = 'back-to-top neon-back-to-top';
    btn.setAttribute('aria-label', 'Back to top');
    btn.innerHTML = '<i class="fas fa-chevron-up neon-icon-glow"></i>';

    document.body.appendChild(btn);

    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        btn.classList.add('visible');
      } else {
        btn.classList.remove('visible');
      }
    };

    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    };

    btn.addEventListener('click', scrollToTop);
    window.addEventListener('scroll', toggleVisibility);
  })();

  // Table of Contents for Single Posts
  const postTOC = (() => {
    const toc = document.getElementById('post-toc');
    if (!toc) return;

    const contentArea = document.querySelector('.content-area, .neon-content-area');
    if (!contentArea) return;

    const headings = contentArea.querySelectorAll('h2, h3');
    if (headings.length === 0) {
      toc.style.display = 'none';
      return;
    }

    const ul = toc.querySelector('nav ul');
    ul.innerHTML = '';

    headings.forEach((heading, index) => {
      if (!heading.id) {
        heading.id = 'heading-' + index;
      }
      const li = document.createElement('li');
      li.className = heading.tagName.toLowerCase() + ' neon-toc-item';
      const a = document.createElement('a');
      a.href = '#' + heading.id;
      a.textContent = heading.textContent;
      a.className = 'neon-toc-link';
      li.appendChild(a);
      ul.appendChild(li);
    });

    // Smooth scroll for TOC links
    toc.querySelectorAll('a').forEach(link => {
      link.addEventListener('click', e => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const target = document.getElementById(targetId);
        if (target) {
          window.scrollTo({
            top: target.offsetTop - 80,
            behavior: 'smooth'
          });
        }
      });
    });

    // Make TOC fixed on scroll with neon effect
    const offsetTop = toc.offsetTop;
    window.addEventListener('scroll', () => {
      if (window.scrollY > offsetTop) {
        toc.classList.add('fixed');
        // Add pulsing neon effect when fixed
        toc.classList.add('neon-pulse');
      } else {
        toc.classList.remove('fixed');
        toc.classList.remove('neon-pulse');
      }
      
      // Highlight current section in TOC
      const tocLinks = toc.querySelectorAll('a');
      const scrollPos = window.scrollY + 100;
      
      headings.forEach((heading, index) => {
        if (heading.offsetTop <= scrollPos) {
          // Remove active class from all links
          tocLinks.forEach(link => link.classList.remove('active'));
          // Add active class to current link
          if (tocLinks[index]) {
            tocLinks[index].classList.add('active');
          }
        }
      });
    });
  })();

  // Dark/Light Mode Toggle
  const themeToggle = (() => {
    const toggleButton = document.getElementById('theme-toggle');
    if (!toggleButton) return;

    const darkClass = 'dark-mode';
    const body = document.body;

    // Set dark mode as default
    if (!localStorage.getItem('theme')) {
      localStorage.setItem('theme', 'dark');
      body.classList.add(darkClass);
    }

    // Load saved theme from localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      body.classList.add(darkClass);
      toggleButton.innerHTML = '<i class="fas fa-sun neon-icon-glow"></i>';
    } else {
      body.classList.remove(darkClass);
      toggleButton.innerHTML = '<i class="fas fa-moon neon-icon-glow"></i>';
    }

    const toggleTheme = () => {
      if (body.classList.contains(darkClass)) {
        body.classList.remove(darkClass);
        toggleButton.innerHTML = '<i class="fas fa-moon neon-icon-glow"></i>';
        localStorage.setItem('theme', 'light');
      } else {
        body.classList.add(darkClass);
        toggleButton.innerHTML = '<i class="fas fa-sun neon-icon-glow"></i>';
        localStorage.setItem('theme', 'dark');
      }
    };

    toggleButton.addEventListener('click', toggleTheme);
  })();

  // Hover Effects and Microinteractions for Cards and Buttons
  const hoverEffects = (() => {
    // Cards hover effect with neon glow
    const cards = document.querySelectorAll('.card, .tool-card, .neon-tool-card');
    cards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.classList.add('hovered');
        card.style.transition = 'all 0.3s ease';
        card.style.boxShadow = '0 0 20px rgba(191, 0, 255, 0.7), 0 0 40px rgba(0, 255, 255, 0.4)';
      });
      card.addEventListener('mouseleave', () => {
        card.classList.remove('hovered');
        card.style.transition = 'all 0.5s ease';
        card.style.boxShadow = '';
      });
    });

    // Neon buttons microinteraction
    const buttons = document.querySelectorAll('button, .button, a.button, .neon-button');
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        button.classList.add('neon-hover');
      });
      button.addEventListener('mouseleave', () => {
        button.classList.remove('neon-hover');
      });
      button.addEventListener('mousedown', () => {
        button.classList.add('pressed');
        button.style.transform = 'translateY(2px) scale(0.98)';
      });
      button.addEventListener('mouseup', () => {
        button.classList.remove('pressed');
        button.style.transform = '';
      });
      button.addEventListener('mouseleave', () => {
        button.classList.remove('pressed');
        button.style.transform = '';
      });
    });
    
    // Add glitch effect to neon text on hover
    const neonTexts = document.querySelectorAll('.neon-title, .neon-section-title, h1, h2');
    neonTexts.forEach(text => {
      text.addEventListener('mouseenter', () => {
        // Add glitch animation class
        text.classList.add('neon-glitch');
        
        // Remove animation after a delay
        setTimeout(() => {
          text.classList.remove('neon-glitch');
        }, 1000);
      });
    });
  })();

  // AJAX Filtering for Blog Archive
  (function () {
    const categoryButtons = document.querySelectorAll('.category-button, .neon-category-button');
    const blogGrid = document.querySelector('.blog-grid, .neon-blog-grid');

    if (!categoryButtons.length || !blogGrid) return;

    categoryButtons.forEach(button => {
      button.addEventListener('click', e => {
        e.preventDefault();
        const category = button.getAttribute('data-category');
        if (!category) return;

        // Highlight active button
        categoryButtons.forEach(btn => {
          btn.classList.remove('active');
          btn.setAttribute('aria-pressed', 'false');
        });
        button.classList.add('active');
        button.setAttribute('aria-pressed', 'true');

        // Add neon glow effect to active button
        button.style.boxShadow = '0 0 15px var(--neon-purple), 0 0 30px var(--neon-cyan)';

        // Show loading state with neon spinner
        blogGrid.classList.add('loading');
        
        // Create loading spinner
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'neon-spinner';
        blogGrid.appendChild(loadingSpinner);

        // AJAX request
        fetch(aiInspiredShifterVars.ajaxUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          },
          body: new URLSearchParams({
            action: 'filter_blog',
            category: category,
          }),
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Add fade effect for smooth transition
              blogGrid.style.opacity = 0;
              setTimeout(() => {
                blogGrid.innerHTML = data.data;
                blogGrid.style.opacity = 1;
              }, 300);
            } else {
              blogGrid.innerHTML = '<p class="neon-text">No posts found.</p>';
            }
            blogGrid.classList.remove('loading');
          })
          .catch(() => {
            blogGrid.innerHTML = '<p class="neon-text">Error loading posts.</p>';
            blogGrid.classList.remove('loading');
          });
      });
    });
  })();

  // AJAX Filtering and Sorting for AI Tools Directory
  (function () {
    const filters = document.querySelectorAll('.filter-select[data-filter], .neon-filter-select[data-filter]');
    const toolsGrid = document.querySelector('.tools-grid, .neon-tools-grid');

    if (!filters.length || !toolsGrid) return;

    const getFilterValues = () => {
      const values = {};
      filters.forEach(filter => {
        values[filter.getAttribute('data-filter')] = filter.value;
      });
      return values;
    };

    const fetchTools = () => {
      const filterValues = getFilterValues();

      // Show loading state with neon spinner
      toolsGrid.classList.add('loading');
      
      // Create loading spinner if it doesn't exist
      if (!document.querySelector('.neon-spinner')) {
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'neon-spinner';
        toolsGrid.appendChild(loadingSpinner);
      }

      fetch(aiInspiredShifterVars.ajaxUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        body: new URLSearchParams({
          action: 'filter_ai_tools',
          category: filterValues.category || '',
          pricing: filterValues.pricing || '',
          feature: filterValues.feature || '',
          sort: filterValues.sort || 'date',
        }),
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Add fade effect for smooth transition
            toolsGrid.style.opacity = 0;
            setTimeout(() => {
              toolsGrid.innerHTML = data.data;
              toolsGrid.style.opacity = 1;
              
              // Initialize hover effects for new cards
              const newCards = toolsGrid.querySelectorAll('.tool-card, .neon-tool-card');
              newCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                  card.classList.add('hovered');
                  card.style.boxShadow = '0 0 20px rgba(191, 0, 255, 0.7), 0 0 40px rgba(0, 255, 255, 0.4)';
                });
                card.addEventListener('mouseleave', () => {
                  card.classList.remove('hovered');
                  card.style.boxShadow = '';
                });
              });
            }, 300);
          } else {
            toolsGrid.innerHTML = '<p class="neon-text">No AI tools found.</p>';
          }
          toolsGrid.classList.remove('loading');
        })
        .catch(() => {
          toolsGrid.innerHTML = '<p class="neon-text">Error loading AI tools.</p>';
          toolsGrid.classList.remove('loading');
        });
    };

    filters.forEach(filter => {
      filter.addEventListener('change', () => {
        // Add neon glow effect to selected filter
        filter.style.boxShadow = '0 0 10px var(--neon-purple), 0 0 20px var(--neon-cyan)';
        fetchTools();
      });
    });

    // Initial fetch on page load
    fetchTools();
  })();

  // Smooth Scroll for Anchor Links (e.g. Table of Contents)
  const smoothAnchorScroll = (() => {
    const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');
    anchorLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          e.preventDefault();
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
          // Update URL hash without jumping
          history.pushState(null, null, '#' + targetId);
          
          // Add brief highlight effect to target element
          targetElement.classList.add('neon-highlight');
          setTimeout(() => {
            targetElement.classList.remove('neon-highlight');
          }, 1000);
        }
      });
    });
  })();

  // Add CSS for neon spinner, glitch effect, and highlight effect
  const addStyles = (() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Neon Spinner */
      .neon-spinner {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #BF00FF;
        border-bottom-color: #00FFFF;
        animation: neon-spin 1s linear infinite;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -25px;
        margin-left: -25px;
        box-shadow: 0 0 15px #BF00FF, 0 0 30px #00FFFF;
      }
      
      @keyframes neon-spin {
        0% {
          transform: rotate(0deg);
          border-top-color: #BF00FF;
          border-bottom-color: #00FFFF;
        }
        50% {
          border-top-color: #00FFFF;
          border-bottom-color: #BF00FF;
        }
        100% {
          transform: rotate(360deg);
          border-top-color: #BF00FF;
          border-bottom-color: #00FFFF;
        }
      }
      
      /* Neon Glitch Effect */
      @keyframes neon-glitch {
        0% {
          text-shadow: 0 0 5px #FFA500, 0 0 10px #FFA500;
          transform: translateX(0);
        }
        20% {
          text-shadow: -3px 0 5px #BF00FF, 3px 0 10px #00FFFF;
          transform: translateX(-2px);
        }
        40% {
          text-shadow: 3px 0 5px #00FFFF, -3px 0 10px #BF00FF;
          transform: translateX(2px);
        }
        60% {
          text-shadow: 0 0 15px #FFA500, 0 0 20px #BF00FF;
          transform: translateX(0);
        }
        80% {
          text-shadow: -2px 0 5px #BF00FF, 2px 0 10px #00FFFF;
          transform: translateX(-1px);
        }
        100% {
          text-shadow: 0 0 5px #FFA500, 0 0 10px #FFA500;
          transform: translateX(0);
        }
      }
      
      .neon-glitch {
        animation: neon-glitch 0.5s ease forwards;
      }
      
      /* Neon Highlight Effect */
      @keyframes neon-highlight-pulse {
        0% {
          box-shadow: 0 0 5px rgba(255, 165, 0, 0.5), 0 0 10px rgba(191, 0, 255, 0.3);
        }
        50% {
          box-shadow: 0 0 20px rgba(255, 165, 0, 0.8), 0 0 40px rgba(0, 255, 255, 0.6);
        }
        100% {
          box-shadow: 0 0 5px rgba(255, 165, 0, 0.5), 0 0 10px rgba(191, 0, 255, 0.3);
        }
      }
      
      .neon-highlight {
        animation: neon-highlight-pulse 1s ease;
      }
      
      /* Back to Top Button */
      .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #0a0a0a;
        color: #FFA500;
        border: 2px solid #FFA500;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow: 0 0 10px rgba(255, 165, 0, 0.5), 0 0 20px rgba(191, 0, 255, 0.3);
      }
      
      .back-to-top.visible {
        opacity: 1;
        visibility: visible;
      }
      
      .back-to-top:hover {
        background: #222;
        box-shadow: 0 0 15px rgba(255, 165, 0, 0.8), 0 0 30px rgba(191, 0, 255, 0.5);
        transform: translateY(-3px);
      }
    `;
    document.head.appendChild(style);
  })();

})();