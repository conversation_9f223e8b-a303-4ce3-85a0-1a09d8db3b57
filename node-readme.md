# Node.js in B:\MyRooProject

This directory contains a local installation of Node.js v22.14.0, which includes npm v10.9.2 and npx v10.9.2.

## How to use npm and npx

### Option 1: Use the Node.js Terminal

Run the `node-terminal.bat` file to open a PowerShell window with npm and npx commands available:

```
.\node-terminal.bat
```

In this terminal, you can use npm and npx commands directly:

```
npm -v
npx -v
```

### Option 2: Direct Path

You can also run npm and npx directly using their full paths:

```
B:\MyRooProject\node-v22.14.0-win-x64\npm.cmd -v
B:\MyRooProject\node-v22.14.0-win-x64\npx.cmd -v
```

### Option 3: Load the PowerShell Module

In an existing PowerShell window, you can load the Node.js tools:

```powershell
. .\node-tools.ps1
npm -v
npx -v
```

## Examples

### Initialize a new Node.js project

```
npm init -y
```

### Install a package

```
npm install express
```

### Run a package with npx

```
npx cowsay "Hello, Node.js!"
```
