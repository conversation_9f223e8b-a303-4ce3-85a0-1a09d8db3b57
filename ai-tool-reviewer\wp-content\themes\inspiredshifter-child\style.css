/*
Theme Name: InspiredShifter Child
Theme URI: https://inspiredshifter.com
Description: Child theme for AI InspiredShifter based on Astra
Version: 1.0.0
Author: AI InspiredShifter
Author URI: https://inspiredshifter.com
Template: astra
Text Domain: inspiredshifter-child
*/

/* ======================================
   VARIABLES
====================================== */
:root {
    /* Colors */
    --primary-blue: #3a5cc9;
    --dark-blue: #2d49a0;
    --light-blue: #99CCFF;
    --text-gray: #4a5568;
    --title-gray: #2d3748;
    --background-gray: #f5f7fa;
    --border-gray: #e0e0e0;
    --orange-accent: #ff7f45;
    --teal-accent: #32CDB8;
    --purple-accent: #7e5bef;
    
    /* Typography */
    --heading-font: 'Montserrat', sans-serif;
    --body-font: 'Open Sans', sans-serif;
    
    /* Spacing */
    --section-padding: 80px;
    --section-padding-mobile: 60px;
    --container-width: 1200px;
    
    /* UI Elements */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
}

/* ======================================
   BASE STYLES
====================================== */
body {
    font-family: var(--body-font);
    color: var(--text-gray);
    line-height: 1.6;
    background-color: #ffffff;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    color: var(--title-gray);
    line-height: 1.3;
    margin-top: 0;
    font-weight: 700;
}

h1 {
    font-size: 46px;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}

h2 {
    font-size: 32px;
    margin-bottom: 15px;
    letter-spacing: -0.3px;
}

h3 {
    font-size: 24px;
    margin-bottom: 12px;
    font-weight: 600;
}

h4 {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 600;
}

h5 {
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 600;
}

h6 {
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

p {
    margin-bottom: 1.5rem;
    font-size: 16px;
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--dark-blue);
}

img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
}

.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Button Styles */
.button,
button,
input[type="button"],
input[type="submit"] {
    display: inline-block;
    padding: 12px 24px;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-family: var(--heading-font);
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition-fast);
    text-align: center;
    line-height: 1.4;
    box-shadow: 0 4px 6px rgba(58, 92, 201, 0.2);
}

.button:hover,
button:hover,
input[type="button"]:hover,
input[type="submit"]:hover {
    background-color: var(--dark-blue);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(58, 92, 201, 0.25);
}

/* ======================================
   HEADER & NAVIGATION
====================================== */
.site-header {
    background-color: #ffffff;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition-fast);
}

.site-logo {
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 24px;
    color: var(--primary-blue);
}

.site-logo a {
    color: var(--primary-blue);
    text-decoration: none;
}

/* Main Navigation */
.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    margin-left: 30px;
    position: relative;
}

.main-navigation a {
    color: var(--text-gray);
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: var(--transition-fast);
    padding: 8px 0;
    position: relative;
}

.main-navigation a:hover {
    color: var(--primary-blue);
}

/* ======================================
   HERO SECTION
====================================== */
.hero-section {
    background-image: linear-gradient(135deg, rgba(45, 73, 160, 0.8), rgba(58, 92, 201, 0.8)), url('https://placehold.co/1920x800/e2e8f0/a0aec0?text=Hero+Background+Image');
    background-size: cover;
    background-position: center;
    color: white;
    position: relative;
    padding: var(--section-padding) 0;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    color: white;
    margin-bottom: 20px;
    font-size: 46px;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content p {
    font-size: 18px;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-align: center;
    transition: var(--transition-fast);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-blue {
    background-color: var(--primary-blue);
    color: white;
    border: 2px solid var(--primary-blue);
}

.cta-blue:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.cta-white {
    background-color: white;
    color: var(--primary-blue);
    border: 2px solid white;
}

.cta-white:hover {
    background-color: transparent;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* ======================================
   FEATURED TOOLS SECTION
====================================== */
.featured-tools-section {
    padding: var(--section-padding) 0;
    background-color: white;
    position: relative;
    overflow: hidden;
}

.section-title {
    text-align: center;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.section-description {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 40px;
    position: relative;
    z-index: 1;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    position: relative;
    z-index: 1;
}

.tool-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: 30px;
    box-shadow: var(--box-shadow);
    transition: var(--transition-medium);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(224, 224, 224, 0.3);
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.tool-logo {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tool-logo-placeholder {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.popular-badge {
    background: linear-gradient(90deg, var(--teal-accent), #2bb3a0);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(50, 205, 184, 0.3);
}

.tool-name {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--title-gray);
}

.tool-description {
    color: var(--text-gray);
    font-size: 15px;
    margin-bottom: 25px;
    flex-grow: 1;
    line-height: 1.6;
}

.tool-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.pricing-info {
    font-size: 14px;
    font-weight: 600;
    color: var(--title-gray);
    background-color: var(--background-gray);
    padding: 6px 12px;
    border-radius: 20px;
}

.learn-more-btn {
    display: inline-block;
    padding: 10px 20px;
    border: 1px solid var(--primary-blue);
    border-radius: var(--border-radius-sm);
    color: var(--primary-blue);
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition-fast);
    background-color: transparent;
}

.learn-more-btn:hover {
    background-color: var(--primary-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(58, 92, 201, 0.2);
}

/* ======================================
   RESOURCES SECTION
====================================== */
.resources-section {
    padding: var(--section-padding) 0;
    background-color: var(--background-gray);
    position: relative;
    overflow: hidden;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    position: relative;
    z-index: 1;
}

.resource-card {
    background-color: white;
    border-radius: var(--border-radius-lg);
    padding: 40px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.resource-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.resource-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background-color: var(--background-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: var(--primary-blue);
}

.resource-card h3 {
    margin-bottom: 20px;
    font-size: 22px;
}

.resource-card p {
    margin-bottom: 30px;
    color: var(--text-gray);
}

.resource-btn {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-blue);
    color: white;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: var(--transition-fast);
    box-shadow: 0 4px 10px rgba(58, 92, 201, 0.2);
}

.resource-btn:hover {
    background-color: var(--dark-blue);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(58, 92, 201, 0.25);
}

/* ======================================
   CATEGORIES SECTION
====================================== */
.categories-section {
    padding: var(--section-padding) 0;
    background-color: white;
    position: relative;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    position: relative;
    z-index: 1;
}

.category-card {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition-medium);
    background-color: white;
    position: relative;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.category-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-fast);
}

.category-image-placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.category-card:hover .category-image,
.category-card:hover .category-image-placeholder {
    transform: scale(1.05);
}

.category-content {
    padding: 25px;
    position: relative;
}

.category-title {
    margin-bottom: 15px;
    font-size: 20px;
    position: relative;
}

.category-description {
    font-size: 15px;
    margin-bottom: 25px;
    color: var(--text-gray);
    line-height: 1.6;
}

.category-btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-blue);
    color: white;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    box-shadow: 0 4px 8px rgba(58, 92, 201, 0.2);
}

.category-btn:hover {
    opacity: 0.9;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(58, 92, 201, 0.25);
}

/* ======================================
   RESPONSIVE ADJUSTMENTS
====================================== */
@media (max-width: 1024px) {
    h1 {
        font-size: 36px;
    }
    
    h2 {
        font-size: 28px;
    }
    
    .tools-grid,
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 32px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .tools-grid,
    .resources-grid,
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .section-padding {
        padding: var(--section-padding-mobile) 0;
    }
}

/* Fix for Astra compatibility */
.ast-container {
    max-width: 100%;
    padding: 0;
}

.site-content .ast-container {
    display: block;
    padding: 0;
}
