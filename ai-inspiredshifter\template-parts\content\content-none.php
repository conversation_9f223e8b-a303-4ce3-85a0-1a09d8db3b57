<?php
/**
 * Template part for displaying a message that posts cannot be found
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

?>

<section class="no-results not-found">
    <div class="no-results-content">
        <div class="no-results-icon">
            <i class="fas fa-file-alt"></i>
        </div>
        
        <header class="page-header">
            <h1 class="page-title"><?php esc_html_e('Nothing Found', 'ai-inspiredshifter'); ?></h1>
        </header><!-- .page-header -->

        <div class="page-content">
            <?php
            if (is_home() && current_user_can('publish_posts')) :
                printf(
                    '<p>' . wp_kses(
                        /* translators: 1: link to WP admin new post page. */
                        __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'ai-inspiredshifter'),
                        array(
                            'a' => array(
                                'href' => array(),
                            ),
                        )
                    ) . '</p>',
                    esc_url(admin_url('post-new.php'))
                );
            elseif (is_search()) :
                ?>
                <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'ai-inspiredshifter'); ?></p>
                <div class="search-form-container">
                    <?php get_search_form(); ?>
                </div>
                <?php
            elseif (is_post_type_archive('ai_tool')) :
                ?>
                <p><?php esc_html_e('No AI tools have been added yet. Check back soon!', 'ai-inspiredshifter'); ?></p>
                <?php
            else :
                ?>
                <p><?php esc_html_e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'ai-inspiredshifter'); ?></p>
                <div class="search-form-container">
                    <?php get_search_form(); ?>
                </div>
                <?php
            endif;
            ?>
        </div><!-- .page-content -->
    </div>
</section><!-- .no-results -->
