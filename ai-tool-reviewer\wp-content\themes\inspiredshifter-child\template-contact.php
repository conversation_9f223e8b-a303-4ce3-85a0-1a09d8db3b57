<?php
/**
 * Template Name: Contact Page
 *
 * @package InspiredShifter Child
 */

get_header();
?>

<div class="contact-page">
    <div class="contact-hero">
        <div class="container">
            <div class="contact-hero-content">
                <h1 class="contact-title"><?php the_title(); ?></h1>
                
                <?php if (get_field('contact_subtitle')) : ?>
                    <p class="contact-subtitle"><?php echo get_field('contact_subtitle'); ?></p>
                <?php else : ?>
                    <p class="contact-subtitle"><?php _e('We\'d love to hear from you. Use the form below to get in touch with our team.', 'inspiredshifter-child'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="contact-content">
        <div class="container">
            <div class="contact-layout">
                <div class="contact-form-container">
                    <h2 class="section-title"><?php _e('Send Us a Message', 'inspiredshifter-child'); ?></h2>
                    
                    <?php 
                    // Check if Contact Form 7 is active and a form ID is provided
                    if (function_exists('wpcf7_contact_form_tag_func') && get_field('contact_form_shortcode')) {
                        echo do_shortcode(get_field('contact_form_shortcode'));
                    } else {
                        // Fallback contact form HTML if Contact Form 7 is not available
                    ?>
                        <form class="contact-form" method="post">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name"><?php _e('Name', 'inspiredshifter-child'); ?> <span class="required">*</span></label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email"><?php _e('Email', 'inspiredshifter-child'); ?> <span class="required">*</span></label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject"><?php _e('Subject', 'inspiredshifter-child'); ?> <span class="required">*</span></label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="message"><?php _e('Message', 'inspiredshifter-child'); ?> <span class="required">*</span></label>
                                <textarea id="message" name="message" rows="6" required></textarea>
                            </div>
                            
                            <div class="form-submit">
                                <button type="submit" class="submit-button"><?php _e('Send Message', 'inspiredshifter-child'); ?></button>
                            </div>
                        </form>
                    <?php } ?>
                </div>
                
                <div class="contact-info-container">
                    <div class="contact-info">
                        <h2 class="section-title"><?php _e('Contact Information', 'inspiredshifter-child'); ?></h2>
                        
                        <div class="info-items">
                            <?php if (get_field('email_address')) : ?>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                                    </div>
                                    <div class="info-content">
                                        <h3><?php _e('Email', 'inspiredshifter-child'); ?></h3>
                                        <p><a href="mailto:<?php echo esc_attr(get_field('email_address')); ?>"><?php echo esc_html(get_field('email_address')); ?></a></p>
                                    </div>
                                </div>
                            <?php else : ?>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                                    </div>
                                    <div class="info-content">
                                        <h3><?php _e('Email', 'inspiredshifter-child'); ?></h3>
                                        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_field('phone_number')) : ?>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                                    </div>
                                    <div class="info-content">
                                        <h3><?php _e('Phone', 'inspiredshifter-child'); ?></h3>
                                        <p><a href="tel:<?php echo esc_attr(get_field('phone_number')); ?>"><?php echo esc_html(get_field('phone_number')); ?></a></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_field('office_address')) : ?>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                                    </div>
                                    <div class="info-content">
                                        <h3><?php _e('Address', 'inspiredshifter-child'); ?></h3>
                                        <p><?php echo nl2br(esc_html(get_field('office_address'))); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="info-item">
                                <div class="info-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                                </div>
                                <div class="info-content">
                                    <h3><?php _e('Business Hours', 'inspiredshifter-child'); ?></h3>
                                    <?php if (get_field('business_hours')) : ?>
                                        <p><?php echo nl2br(esc_html(get_field('business_hours'))); ?></p>
                                    <?php else : ?>
                                        <p><?php _e('Monday - Friday: 9:00 AM - 5:00 PM', 'inspiredshifter-child'); ?><br>
                                        <?php _e('Saturday - Sunday: Closed', 'inspiredshifter-child'); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (have_rows('social_media_links', 'option') || get_field('social_media_links')) : 
                            $social_links = get_field('social_media_links') ? get_field('social_media_links') : get_field('social_media_links', 'option');
                        ?>
                            <div class="social-links">
                                <h3><?php _e('Follow Us', 'inspiredshifter-child'); ?></h3>
                                <div class="social-icons">
                                    <?php while (have_rows('social_media_links', $social_links ? '' : 'option')) : the_row(); 
                                        $platform = get_sub_field('platform');
                                        $url = get_sub_field('url');
                                        
                                        if ($url) :
                                            $icon = '';
                                            switch (strtolower($platform)) {
                                                case 'facebook':
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/></svg>';
                                                    break;
                                                case 'twitter':
                                                case 'x':
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>';
                                                    break;
                                                case 'instagram':
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>';
                                                    break;
                                                case 'linkedin':
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>';
                                                    break;
                                                case 'youtube':
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/></svg>';
                                                    break;
                                                default:
                                                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm1 16.057v-3.057h2.994c-.059 1.143-.212 2.24-.456 3.279-.823-.12-1.674-.188-2.538-.222zm1.957 2.162c-.499 1.33-1.159 2.497-1.957 3.456v-3.62c.666.028 1.319.081 1.957.164zm-1.957-7.219v-3.015c.868-.034 1.721-.103 2.548-.224.238 1.027.389 2.111.446 3.239h-2.994zm0-5.014v-3.661c.806.969 1.471 2.15 1.971 3.496-.642.084-1.3.137-1.971.165zm2.703-3.267c1.237.496 2.354 1.228 3.29 2.146-.642.234-1.311.442-2.019.607-.344-.992-.775-1.91-1.271-2.753zm-7.241 13.56c-.244-1.039-.398-2.136-.456-3.279h2.994v3.057c-.865.034-1.714.102-2.538.222zm2.538 1.776v3.62c-.798-.959-1.458-2.126-1.957-3.456.638-.083 1.291-.136 1.957-.164zm-2.994-7.055c.057-1.128.207-2.212.446-3.239.827.121 1.68.19 2.548.224v3.015h-2.994zm1.024-5.179c.5-1.346 1.165-2.527 1.97-3.496v3.661c-.671-.028-1.329-.081-1.97-.165zm-2.005-.35c-.708-.165-1.377-.373-2.018-.607.937-.918 2.053-1.65 3.29-2.146-.496.844-.927 1.762-1.272 2.753zm-.549 1.918c-.264 1.151-.434 2.36-.492 3.611h-3.933c.165-1.658.739-3.197 1.617-4.518.88.361 1.816.67 2.808.907zm.009 9.262c-.988.236-1.92.542-2.797.9-.89-1.328-1.471-2.879-1.637-4.551h3.934c.058 1.265.231 2.488.5 3.651zm.553 1.917c.342.976.768 1.881 1.257 2.712-1.223-.49-2.326-1.211-3.256-2.115.636-.229 1.299-.435 1.999-.597zm9.924 0c.7.163 1.362.367 1.999.597-.931.903-2.034 1.625-3.257 2.116.489-.832.915-1.737 1.258-2.713zm.553-1.917c.27-1.163.442-2.386.501-3.651h3.934c-.167 1.672-.748 3.223-1.638 4.551-.877-.358-1.81-.664-2.797-.9zm.501-5.651c-.058-1.251-.229-2.46-.492-3.611.992-.237 1.929-.546 2.809-.907.877 1.321 1.451 2.86 1.616 4.518h-3.933z"/></svg>';
                                            }
                                    ?>
                                        <a href="<?php echo esc_url($url); ?>" class="social-icon" target="_blank" rel="noopener">
                                            <span class="screen-reader-text"><?php echo esc_html($platform); ?></span>
                                            <?php echo $icon; ?>
                                        </a>
                                    <?php endif; ?>
                                    <?php endwhile; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (get_field('show_map') || !get_field('map_embed_code')) : ?>
        <div class="contact-map">
            <?php if (get_field('map_embed_code')) : ?>
                <div class="map-embed">
                    <?php echo get_field('map_embed_code'); ?>
                </div>
            <?php else : ?>
                <div class="map-placeholder">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/map-placeholder.jpg" alt="<?php _e('Map Placeholder', 'inspiredshifter-child'); ?>">
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div class="contact-faq">
        <div class="container">
            <h2 class="section-title"><?php _e('Frequently Asked Questions', 'inspiredshifter-child'); ?></h2>
            
            <?php if (have_rows('contact_faqs')) : ?>
                <div class="faq-list">
                    <?php while (have_rows('contact_faqs')) : the_row(); 
                        $question = get_sub_field('question');
                        $answer = get_sub_field('answer');
                    ?>
                        <div class="faq-item">
                            <div class="faq-question"><?php echo esc_html($question); ?></div>
                            <div class="faq-answer"><?php echo $answer; ?></div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <!-- Default FAQs if none are added in ACF -->
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question"><?php _e('How quickly can I expect a response?', 'inspiredshifter-child'); ?></div>
                        <div class="faq-answer">
                            <p><?php _e('We typically respond to all inquiries within 24-48 business hours. For urgent matters, please indicate this in the subject line of your message.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question"><?php _e('Do you offer partnerships or collaborations?', 'inspiredshifter-child'); ?></div>
                        <div class="faq-answer">
                            <p><?php _e('Yes, we\'re open to partnerships and collaborations with AI tool providers and other businesses in the tech space. Please use the contact form and select "Partnership" as the subject to discuss potential opportunities.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question"><?php _e('How can I suggest an AI tool for review?', 'inspiredshifter-child'); ?></div>
                        <div class="faq-answer">
                            <p><?php _e('We\'re always looking for new and innovative AI tools to review. Please use the contact form with the subject "Tool Suggestion" and provide details about the tool you\'d like us to review.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
(function($) {
    // FAQ accordion functionality
    $('.faq-question').on('click', function() {
        $(this).toggleClass('active');
        $(this).next('.faq-answer').slideToggle(300);
    });
    
    // Form validation and submission handling
    $('.contact-form').on('submit', function(e) {
        // This would be replaced with actual form submission logic
        // For demo purposes, we're just preventing the default action
        e.preventDefault();
        
        // Simple validation
        var valid = true;
        $(this).find('[required]').each(function() {
            if (!$(this).val()) {
                valid = false;
                $(this).addClass('error');
            } else {
                $(this).removeClass('error');
            }
        });
        
        if (valid) {
            // Show success message
            $(this).fadeOut(300, function() {
                $(this).after('<div class="form-success"><h3><?php _e("Thank You!", "inspiredshifter-child"); ?></h3><p><?php _e("Your message has been sent. We\'ll get back to you as soon as possible.", "inspiredshifter-child"); ?></p></div>');
            });
        }
    });
})(jQuery);
</script>

<?php get_footer(); ?>