/**
 * InspiredShifter Child Theme - Modern Styles
 * Additional modern and futuristic styling elements
 */

/* ======================================
   MODERN UI ELEMENTS
====================================== */

/* Glassmorphism Cards */
.glass-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Gradient Buttons */
.gradient-button {
    background: linear-gradient(90deg, var(--primary-blue), var(--purple-accent));
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.gradient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--purple-accent), var(--primary-blue));
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.gradient-button:hover::before {
    opacity: 1;
}

.gradient-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(58, 92, 201, 0.3);
}

/* Floating Elements */
.floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Glow Effects */
.glow-text {
    text-shadow: 0 0 10px rgba(58, 92, 201, 0.5);
}

.glow-box {
    box-shadow: 0 0 15px rgba(58, 92, 201, 0.3);
}

/* Animated Gradients */
.animated-gradient {
    background: linear-gradient(-45deg, var(--primary-blue), var(--purple-accent), var(--teal-accent), var(--dark-blue));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Hover Card Effects */
.hover-card {
    transition: all 0.5s ease;
    transform-style: preserve-3d;
}

.hover-card:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Modern Badges */
.modern-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    background: linear-gradient(90deg, var(--teal-accent), #2bb3a0);
    color: white;
    box-shadow: 0 4px 10px rgba(50, 205, 184, 0.3);
}

/* Animated Icons */
.animated-icon {
    transition: all 0.3s ease;
}

.animated-icon:hover {
    transform: scale(1.2);
}

/* Futuristic Dividers */
.futuristic-divider {
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
    margin: 40px 0;
    position: relative;
}

.futuristic-divider::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--primary-blue);
    border-radius: 50%;
    top: -3.5px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 10px var(--primary-blue);
}

/* Neon Outlines */
.neon-outline {
    border: 2px solid var(--primary-blue);
    box-shadow: 0 0 10px var(--primary-blue), inset 0 0 10px var(--primary-blue);
}

/* ======================================
   ENHANCED HEADER STYLES
====================================== */
.modern-header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modern-header .site-logo {
    font-weight: 700;
    letter-spacing: -0.5px;
}

.modern-header .main-navigation a {
    position: relative;
    padding: 8px 0;
}

.modern-header .main-navigation a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue), var(--purple-accent));
    transition: width 0.3s ease;
}

.modern-header .main-navigation a:hover::after {
    width: 100%;
}

.modern-header .sign-up-button {
    background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
    border-radius: 6px;
    box-shadow: 0 4px 10px rgba(58, 92, 201, 0.3);
    transition: all 0.3s ease;
}

.modern-header .sign-up-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(58, 92, 201, 0.4);
}

/* ======================================
   ENHANCED HERO SECTION
====================================== */
.modern-hero {
    position: relative;
    overflow: hidden;
}

.modern-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(45, 73, 160, 0.9), rgba(58, 92, 201, 0.8));
    z-index: 1;
}

.modern-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 50%, rgba(126, 91, 239, 0.4) 0%, rgba(58, 92, 201, 0) 70%);
    z-index: 2;
}

.modern-hero .hero-content {
    position: relative;
    z-index: 3;
}

.modern-hero .hero-title {
    font-weight: 800;
    letter-spacing: -1px;
    margin-bottom: 25px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
}

.modern-hero .hero-description {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
    transition-delay: 0.2s;
}

.modern-hero .cta-buttons {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
    transition-delay: 0.4s;
}

.modern-hero.animated .hero-title,
.modern-hero.animated .hero-description,
.modern-hero.animated .cta-buttons {
    opacity: 1;
    transform: translateY(0);
}

.modern-hero .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.modern-hero .floating-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

/* ======================================
   ENHANCED TOOL CARDS
====================================== */
.modern-tool-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.5s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.modern-tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-blue), var(--purple-accent));
    opacity: 0;
    transition: all 0.3s ease;
}

.modern-tool-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.modern-tool-card:hover::before {
    opacity: 1;
}

.modern-tool-card .tool-logo {
    width: 70px;
    height: 70px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.modern-tool-card:hover .tool-logo {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.modern-tool-card .popular-badge {
    background: linear-gradient(90deg, var(--teal-accent), #2bb3a0);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(50, 205, 184, 0.3);
}

.modern-tool-card .tool-name {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.modern-tool-card:hover .tool-name {
    color: var(--primary-blue);
}

.modern-tool-card .learn-more-btn {
    background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(58, 92, 201, 0.2);
}

.modern-tool-card .learn-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(58, 92, 201, 0.3);
}

/* ======================================
   ENHANCED CATEGORY CARDS
====================================== */
.modern-category-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.5s ease;
    position: relative;
}

.modern-category-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.8) 100%);
    z-index: 1;
    transition: all 0.3s ease;
}

.modern-category-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.modern-category-card:hover::after {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.9) 100%);
}

.modern-category-card .category-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.modern-category-card:hover .category-image {
    transform: scale(1.1);
}

.modern-category-card .category-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30px;
    z-index: 2;
    color: white;
}

.modern-category-card .category-title {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.modern-category-card:hover .category-title {
    transform: translateY(-5px);
}

.modern-category-card .category-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.modern-category-card:hover .category-description {
    opacity: 1;
    transform: translateY(0);
}

.modern-category-card .category-btn {
    background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(20px);
}

.modern-category-card:hover .category-btn {
    opacity: 1;
    transform: translateY(0);
}

.modern-category-card .category-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* ======================================
   RESPONSIVE ADJUSTMENTS
====================================== */
@media (max-width: 1024px) {
    .modern-tool-card:hover,
    .modern-category-card:hover {
        transform: translateY(-10px);
    }
}

@media (max-width: 768px) {
    .modern-hero .hero-title {
        font-size: 36px;
    }
    
    .modern-tool-card:hover,
    .modern-category-card:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 480px) {
    .modern-hero .hero-title {
        font-size: 28px;
    }
    
    .modern-hero .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .modern-tool-card,
    .modern-category-card {
        transform: none !important;
    }
}