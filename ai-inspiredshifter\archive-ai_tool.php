<?php
/**
 * The template for displaying AI Tool archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

:start_line:13
-------
<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <header class="page-header neon-page-header">
            <h1 class="page-title neon-title"><?php esc_html_e('AI Tools Directory', 'ai-inspiredshifter'); ?></h1>
            <div class="archive-description neon-description">
                <p><?php esc_html_e('Discover and explore our comprehensive collection of artificial intelligence tools for various purposes.', 'ai-inspiredshifter'); ?></p>
            </div>
        </header><!-- .page-header -->

        <div class="tools-filter-container neon-tools-filter-container">
            <div class="tools-search neon-tools-search">
                <form role="search" method="get" class="tools-search-form neon-tools-search-form" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="search-field neon-search-field" placeholder="<?php esc_attr_e('Search AI tools...', 'ai-inspiredshifter'); ?>" value="<?php echo get_search_query(); ?>" name="s" />
                    <input type="hidden" name="post_type" value="ai_tool" />
                    <button type="submit" class="search-submit neon-search-submit"><i class="fas fa-search neon-icon"></i></button>
                </form>
            </div>

            <div class="tools-filters neon-tools-filters">
                <div class="filter-group neon-filter-group">
                    <label for="category-filter" class="neon-filter-label"><?php esc_html_e('Category:', 'ai-inspiredshifter'); ?></label>
                    <select id="category-filter" class="filter-select neon-filter-select" data-filter="category">
                        <option value=""><?php esc_html_e('All Categories', 'ai-inspiredshifter'); ?></option>
                        <?php
                        $categories = get_terms(array(
                            'taxonomy' => 'ai_category',
                            'hide_empty' => true,
                        ));
                        
                        if (!empty($categories) && !is_wp_error($categories)) {
                            foreach ($categories as $category) {
                                echo '<option value="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="filter-group neon-filter-group">
                    <label for="pricing-filter" class="neon-filter-label"><?php esc_html_e('Pricing:', 'ai-inspiredshifter'); ?></label>
                    <select id="pricing-filter" class="filter-select neon-filter-select" data-filter="pricing">
                        <option value=""><?php esc_html_e('All Pricing', 'ai-inspiredshifter'); ?></option>
                        <?php
                        $pricing_models = get_terms(array(
                            'taxonomy' => 'pricing_model',
                            'hide_empty' => true,
                        ));
                        
                        if (!empty($pricing_models) && !is_wp_error($pricing_models)) {
                            foreach ($pricing_models as $model) {
                                echo '<option value="' . esc_attr($model->slug) . '">' . esc_html($model->name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="filter-group neon-filter-group">
                    <label for="feature-filter" class="neon-filter-label"><?php esc_html_e('Feature:', 'ai-inspiredshifter'); ?></label>
                    <select id="feature-filter" class="filter-select neon-filter-select" data-filter="feature">
                        <option value=""><?php esc_html_e('All Features', 'ai-inspiredshifter'); ?></option>
                        <?php
                        $features = get_terms(array(
                            'taxonomy' => 'ai_feature',
                            'hide_empty' => true,
                        ));
                        
                        if (!empty($features) && !is_wp_error($features)) {
                            foreach ($features as $feature) {
                                echo '<option value="' . esc_attr($feature->slug) . '">' . esc_html($feature->name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="filter-group neon-filter-group">
                    <label for="sort-filter" class="neon-filter-label"><?php esc_html_e('Sort By:', 'ai-inspiredshifter'); ?></label>
                    <select id="sort-filter" class="filter-select neon-filter-select" data-filter="sort">
                        <option value="date"><?php esc_html_e('Newest', 'ai-inspiredshifter'); ?></option>
                        <option value="title"><?php esc_html_e('Name (A-Z)', 'ai-inspiredshifter'); ?></option>
                        <option value="rating"><?php esc_html_e('Rating (High to Low)', 'ai-inspiredshifter'); ?></option>
                    </select>
                </div>
            </div>
        </div>

        <?php if (have_posts()) : ?>
            <div class="tools-grid neon-tools-grid" id="tools-grid" data-tools-grid>
                <?php
                /* Start the Loop */
                while (have_posts()) :
                    the_post();
                    
                    // Get tool data
                    $rating = get_post_meta(get_the_ID(), 'tool_rating', true);
                    $short_description = get_post_meta(get_the_ID(), 'tool_short_description', true);
                    $pricing_type = get_post_meta(get_the_ID(), 'tool_pricing_type', true);
                    $affiliate_link = get_post_meta(get_the_ID(), 'tool_affiliate_link', true);
                    
                    // Get categories
                    $categories = get_the_terms(get_the_ID(), 'ai_category');
                    $category_classes = '';
                    $category_names = array();
                    
                    if (!empty($categories) && !is_wp_error($categories)) {
                        foreach ($categories as $category) {
                            $category_classes .= ' category-' . $category->slug;
                            $category_names[] = $category->name;
                        }
                    }
                    
                    // Get pricing models
                    $pricing_models = get_the_terms(get_the_ID(), 'pricing_model');
                    $pricing_classes = '';
                    $pricing_names = array();
                    
                    if (!empty($pricing_models) && !is_wp_error($pricing_models)) {
                        foreach ($pricing_models as $model) {
                            $pricing_classes .= ' pricing-' . $model->slug;
                            $pricing_names[] = $model->name;
                        }
                    }
                    
                    // Get features
                    $features = get_the_terms(get_the_ID(), 'ai_feature');
                    $feature_classes = '';
                    $feature_names = array();
                    
                    if (!empty($features) && !is_wp_error($features)) {
                        foreach ($features as $feature) {
                            $feature_classes .= ' feature-' . $feature->slug;
                            $feature_names[] = $feature->name;
                        }
                    }
                ?>
                    <div class="tool-card<?php echo esc_attr($category_classes . $pricing_classes . $feature_classes); ?>" data-rating="<?php echo esc_attr($rating); ?>">
                        <div class="tool-card-inner">
                            <div class="tool-image">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                <?php else : ?>
                                    <a href="<?php the_permalink(); ?>" class="placeholder-image">
                                        <i class="fas fa-robot"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                            
                            <div class="tool-content">
                                <h3 class="tool-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                
                                <?php if ($rating) : ?>
                                    <div class="star-rating">
                                        <?php
                                        $full_stars = floor($rating);
                                        for ($i = 0; $i < $full_stars; $i++) {
                                            echo '<i class="fas fa-star"></i>';
                                        }
                                        
                                        $half_star = $rating - $full_stars;
                                        if ($half_star >= 0.25 && $half_star < 0.75) {
                                            echo '<i class="fas fa-star-half-alt"></i>';
                                            $empty_stars = 5 - $full_stars - 1;
                                        } elseif ($half_star >= 0.75) {
                                            echo '<i class="fas fa-star"></i>';
                                            $empty_stars = 5 - $full_stars - 1;
                                        } else {
                                            $empty_stars = 5 - $full_stars;
                                        }
                                        
                                        for ($i = 0; $i < $empty_stars; $i++) {
                                            echo '<i class="far fa-star"></i>';
                                        }
                                        ?>
                                        <span class="rating-value"><?php echo number_format($rating, 1); ?>/5</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($category_names)) : ?>
                                    <div class="tool-categories">
                                        <?php echo esc_html(implode(', ', $category_names)); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="tool-excerpt">
                                    <?php
                                    if ($short_description) {
                                        echo '<p>' . esc_html($short_description) . '</p>';
                                    } else {
                                        the_excerpt();
                                    }
                                    ?>
                                </div>
                                
                                <div class="tool-meta">
                                    <?php if (!empty($pricing_names)) : ?>
                                        <span class="tool-pricing">
                                            <i class="fas fa-tag"></i> <?php echo esc_html(implode(', ', $pricing_names)); ?>
                                        </span>
                                    <?php elseif ($pricing_type) : ?>
                                        <span class="tool-pricing">
                                            <i class="fas fa-tag"></i> <?php echo esc_html($pricing_type); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="tool-actions">
                                    <a href="<?php the_permalink(); ?>" class="button button-secondary"><?php esc_html_e('Learn More', 'ai-inspiredshifter'); ?></a>
                                    <?php if ($affiliate_link) : ?>
                                        <?php echo do_shortcode('[affiliate_link url="' . esc_url($affiliate_link) . '" text="Try Now" class="button"]'); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>

            <div class="pagination neon-pagination">
                <?php
                the_posts_pagination(array(
                    'mid_size'  => 2,
                    'prev_text' => '<i class="fas fa-chevron-left neon-icon"></i>',
                    'next_text' => '<i class="fas fa-chevron-right neon-icon"></i>',
                ));
                ?>
            </div>

        <?php else : ?>

            <div class="no-tools-found neon-no-tools-found">
                <div class="no-tools-icon neon-no-tools-icon">
                    <i class="fas fa-robot neon-icon-glow"></i>
                </div>
                <h2><?php esc_html_e('No AI Tools Found', 'ai-inspiredshifter'); ?></h2>
                <p><?php esc_html_e('Sorry, no AI tools match your criteria. Try adjusting your filters or search terms.', 'ai-inspiredshifter'); ?></p>
                <a href="<?php echo esc_url(get_post_type_archive_link('ai_tool')); ?>" class="button neon-button"><?php esc_html_e('View All Tools', 'ai-inspiredshifter'); ?></a>
            </div>

        <?php endif; ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
