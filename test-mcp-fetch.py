import requests
import json

# Define the URL to fetch
url_to_fetch = "https://www.example.com"

# Define the MCP server endpoint
base_url = "http://localhost:8080"

# Try different endpoint paths
endpoint_paths = [
    "/tools/fetch",
    "/fetch",
    "/api/fetch",
    "/v1/fetch",
    "/mcp/fetch"
]

# Prepare the request payload
payload = {
    "url": url_to_fetch,
    "max_length": 5000,
    "start_index": 0,
    "raw": False
}

# Try different endpoint paths
for path in endpoint_paths:
    endpoint = f"{base_url}{path}"
    try:
        print(f"\nTrying endpoint: {endpoint}")
        response = requests.post(endpoint, json=payload, timeout=5)
        print(f"Status code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("\nFetched content:")
            print(result.get("content", "No content found"))
            exit(0)
        else:
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"Error with endpoint {endpoint}: {e}")
