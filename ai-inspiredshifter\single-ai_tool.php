<?php
/**
 * The template for displaying single AI Tool posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <?php
        while (have_posts()) :
            the_post();
        ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('single-ai-tool neon-ai-tool'); ?>>
                <div class="tool-header neon-tool-header">
                    <div class="tool-header-content neon-tool-header-content">
                        <div class="tool-categories neon-tool-categories">
                            <?php
                            $categories = get_the_terms(get_the_ID(), 'ai_category');
                            if (!empty($categories) && !is_wp_error($categories)) {
                                foreach ($categories as $category) {
                                    echo '<a href="' . esc_url(get_term_link($category)) . '" class="tool-category neon-tool-category">' . esc_html($category->name) . '</a>';
                                }
                            }
                            ?>
                        </div>

                        <?php the_title('<h1 class="tool-title neon-title">', '</h1>'); ?>

                        <?php if (get_post_meta(get_the_ID(), 'tool_short_description', true)) : ?>
                            <div class="tool-short-description neon-tool-short-description">
                                <?php echo esc_html(get_post_meta(get_the_ID(), 'tool_short_description', true)); ?>
                            </div>
                        <?php endif; ?>

                        <div class="tool-meta neon-tool-meta">
                            <?php
                            // Display rating if available
                            $rating = get_post_meta(get_the_ID(), 'tool_rating', true);
                            if ($rating) :
                                echo do_shortcode('[rating rating="' . esc_attr($rating) . '" class="neon-rating"]');
                            endif;
                            ?>

                            <?php
                            // Display pricing model
                            $pricing_models = get_the_terms(get_the_ID(), 'pricing_model');
                            if (!empty($pricing_models) && !is_wp_error($pricing_models)) :
                            ?>
                                <div class="tool-pricing neon-tool-pricing">
                                    <i class="fas fa-tag neon-icon"></i>
                                    <?php
                                    $pricing_names = array();
                                    foreach ($pricing_models as $model) {
                                        $pricing_names[] = $model->name;
                                    }
                                    echo esc_html(implode(', ', $pricing_names));
                                    ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php
                        // Display affiliate link if available
                        $affiliate_link = get_post_meta(get_the_ID(), 'tool_affiliate_link', true);
                        if ($affiliate_link) :
                        ?>
                            <div class="tool-cta neon-tool-cta">
                                <?php echo do_shortcode('[affiliate_link url="' . esc_url($affiliate_link) . '" text="Try Now" class="button neon-button"]'); ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="tool-featured-image neon-tool-featured-image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('large'); ?>
                        <?php else : ?>
                            <div class="placeholder-image neon-placeholder-image">
                                <i class="fas fa-robot neon-icon-glow"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="tool-content-wrapper neon-tool-content-wrapper">
                    <div class="tool-main-content neon-tool-main-content">
                        <div class="tool-tabs neon-tool-tabs">
                            <ul class="tabs-nav neon-tabs-nav">
                                <li class="active"><a href="#overview" class="neon-tab-link"><?php esc_html_e('Overview', 'ai-inspiredshifter'); ?></a></li>
                                <li><a href="#features" class="neon-tab-link"><?php esc_html_e('Features', 'ai-inspiredshifter'); ?></a></li>
                                <li><a href="#pricing" class="neon-tab-link"><?php esc_html_e('Pricing', 'ai-inspiredshifter'); ?></a></li>
                                <li><a href="#pros-cons" class="neon-tab-link"><?php esc_html_e('Pros & Cons', 'ai-inspiredshifter'); ?></a></li>
                            </ul>

                            <div class="tabs-content neon-tabs-content">
                                <div id="overview" class="tab-content active neon-tab-content">
                                    <h2 class="neon-tab-title"><?php esc_html_e('Overview', 'ai-inspiredshifter'); ?></h2>
                                    <?php the_content(); ?>
                                </div>

                                <div id="features" class="tab-content neon-tab-content">
                                    <h2 class="neon-tab-title"><?php esc_html_e('Features', 'ai-inspiredshifter'); ?></h2>
                                    <?php
                                    // Display features
                                    $features = get_post_meta(get_the_ID(), 'tool_features', true);
                                    if ($features) :
                                        echo '<ul class="tool-features-list neon-features-list">';
                                        $features_array = explode("\n", $features);
                                        foreach ($features_array as $feature) {
                                            if (!empty(trim($feature))) {
                                                echo '<li><i class="fas fa-check neon-icon-glow"></i> ' . esc_html(trim($feature)) . '</li>';
                                            }
                                        }
                                        echo '</ul>';
                                    else :
                                        // Display features taxonomy as fallback
                                        $feature_terms = get_the_terms(get_the_ID(), 'ai_feature');
                                        if (!empty($feature_terms) && !is_wp_error($feature_terms)) :
                                            echo '<ul class="tool-features-list neon-features-list">';
                                            foreach ($feature_terms as $term) {
                                                echo '<li><i class="fas fa-check neon-icon-glow"></i> ' . esc_html($term->name) . '</li>';
                                            }
                                            echo '</ul>';
                                        else :
                                            echo '<p class="neon-text">' . esc_html__('No features specified for this tool.', 'ai-inspiredshifter') . '</p>';
                                        endif;
                                    endif;
                                    ?>
                                </div>

                                <div id="pricing" class="tab-content neon-tab-content">
                                    <h2 class="neon-tab-title"><?php esc_html_e('Pricing', 'ai-inspiredshifter'); ?></h2>
                                    <?php
                                    // Display pricing information
                                    $pricing_type = get_post_meta(get_the_ID(), 'tool_pricing_type', true);
                                    $starting_price = get_post_meta(get_the_ID(), 'tool_starting_price', true);
                                    $pricing_plans = get_post_meta(get_the_ID(), 'tool_pricing_plans', true);
                                    
                                    if ($pricing_type) :
                                        echo '<p class="pricing-type neon-pricing-type"><strong>' . esc_html__('Pricing Type:', 'ai-inspiredshifter') . '</strong> ' . esc_html($pricing_type) . '</p>';
                                    endif;
                                    
                                    if ($starting_price) :
                                        echo '<p class="starting-price neon-starting-price"><strong>' . esc_html__('Starting Price:', 'ai-inspiredshifter') . '</strong> ' . esc_html($starting_price) . '</p>';
                                    endif;
                                    
                                    if ($pricing_plans) :
                                        echo '<div class="pricing-plans neon-pricing-plans">';
                                        echo '<h3 class="neon-pricing-title">' . esc_html__('Pricing Plans', 'ai-inspiredshifter') . '</h3>';
                                        echo '<div class="pricing-plans-content neon-pricing-plans-content">' . wp_kses_post($pricing_plans) . '</div>';
                                        echo '</div>';
                                    else :
                                        echo '<p class="neon-text">' . esc_html__('Detailed pricing information not available. Please visit the official website for the latest pricing details.', 'ai-inspiredshifter') . '</p>';
                                    endif;
                                    
                                    if ($affiliate_link) :
                                        echo '<div class="pricing-cta neon-pricing-cta">';
                                        echo do_shortcode('[affiliate_link url="' . esc_url($affiliate_link) . '" text="Check Current Pricing" class="button neon-button"]');
                                        echo '</div>';
                                    endif;
                                    ?>
                                </div>

                                <div id="pros-cons" class="tab-content neon-tab-content">
                                    <h2 class="neon-tab-title"><?php esc_html_e('Pros & Cons', 'ai-inspiredshifter'); ?></h2>
                                    <div class="pros-cons-container neon-pros-cons-container">
                                        <div class="pros-column neon-pros-column">
                                            <h3 class="neon-pros-title"><?php esc_html_e('Pros', 'ai-inspiredshifter'); ?></h3>
                                            <?php
                                            $pros = get_post_meta(get_the_ID(), 'tool_pros', true);
                                            if ($pros) :
                                                echo '<ul class="pros-list neon-pros-list">';
                                                $pros_array = explode("\n", $pros);
                                                foreach ($pros_array as $pro) {
                                                    if (!empty(trim($pro))) {
                                                        echo '<li><i class="fas fa-plus-circle neon-icon-glow"></i> ' . esc_html(trim($pro)) . '</li>';
                                                    }
                                                }
                                                echo '</ul>';
                                            else :
                                                echo '<p class="neon-text">' . esc_html__('No pros specified for this tool.', 'ai-inspiredshifter') . '</p>';
                                            endif;
                                            ?>
                                        </div>
                                        <div class="cons-column neon-cons-column">
                                            <h3 class="neon-cons-title"><?php esc_html_e('Cons', 'ai-inspiredshifter'); ?></h3>
                                            <?php
                                            $cons = get_post_meta(get_the_ID(), 'tool_cons', true);
                                            if ($cons) :
                                                echo '<ul class="cons-list neon-cons-list">';
                                                $cons_array = explode("\n", $cons);
                                                foreach ($cons_array as $con) {
                                                    if (!empty(trim($con))) {
                                                        echo '<li><i class="fas fa-minus-circle neon-icon-glow"></i> ' . esc_html(trim($con)) . '</li>';
                                                    }
                                                }
                                                echo '</ul>';
                                            else :
                                                echo '<p class="neon-text">' . esc_html__('No cons specified for this tool.', 'ai-inspiredshifter') . '</p>';
                                            endif;
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php
                        // Display screenshots if available
                        $screenshots = get_post_meta(get_the_ID(), 'tool_screenshots', true);
                        if ($screenshots) :
                            $screenshot_ids = explode(',', $screenshots);
                            if (!empty($screenshot_ids)) :
                        ?>
                            <div class="tool-screenshots neon-tool-screenshots">
                                <h2 class="neon-screenshots-title"><?php esc_html_e('Screenshots', 'ai-inspiredshifter'); ?></h2>
                                <div class="screenshots-gallery neon-screenshots-gallery">
                                    <?php
                                    foreach ($screenshot_ids as $id) {
                                        $image_url = wp_get_attachment_image_url($id, 'large');
                                        $image_alt = get_post_meta($id, '_wp_attachment_image_alt', true);
                                        if ($image_url) {
                                            echo '<div class="screenshot-item neon-screenshot-item">';
                                            echo '<a href="' . esc_url($image_url) . '" class="lightbox neon-lightbox">';
                                            echo '<img src="' . esc_url($image_url) . '" alt="' . esc_attr($image_alt) . '">';
                                            echo '</a>';
                                            echo '</div>';
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php
                            endif;
                        endif;
                        ?>

                        <div class="tool-footer neon-tool-footer">
                            <?php echo ai_inspiredshifter_social_sharing(); ?>
                        </div>
                    </div>

                    <div class="tool-sidebar neon-tool-sidebar">
                        <div class="tool-sidebar-widget tool-info neon-tool-info">
                            <h3 class="neon-widget-title"><?php esc_html_e('Tool Information', 'ai-inspiredshifter'); ?></h3>
                            <ul class="neon-info-list">
                                <?php
                                // Display categories
                                if (!empty($categories) && !is_wp_error($categories)) :
                                ?>
                                    <li class="neon-info-item">
                                        <span class="info-label neon-info-label"><i class="fas fa-folder neon-icon"></i> <?php esc_html_e('Category:', 'ai-inspiredshifter'); ?></span>
                                        <span class="info-value neon-info-value">
                                            <?php
                                            $category_names = array();
                                            foreach ($categories as $category) {
                                                $category_names[] = '<a href="' . esc_url(get_term_link($category)) . '" class="neon-link">' . esc_html($category->name) . '</a>';
                                            }
                                            echo implode(', ', $category_names);
                                            ?>
                                        </span>
                                    </li>
                                <?php endif; ?>

                                <?php
                                // Display features
                                $features = get_the_terms(get_the_ID(), 'ai_feature');
                                if (!empty($features) && !is_wp_error($features)) :
                                ?>
                                    <li class="neon-info-item">
                                        <span class="info-label neon-info-label"><i class="fas fa-list neon-icon"></i> <?php esc_html_e('Features:', 'ai-inspiredshifter'); ?></span>
                                        <span class="info-value neon-info-value">
                                            <?php
                                            $feature_names = array();
                                            foreach ($features as $feature) {
                                                $feature_names[] = '<a href="' . esc_url(get_term_link($feature)) . '" class="neon-link">' . esc_html($feature->name) . '</a>';
                                            }
                                            echo implode(', ', $feature_names);
                                            ?>
                                        </span>
                                    </li>
                                <?php endif; ?>

                                <?php if ($pricing_type) : ?>
                                    <li class="neon-info-item">
                                        <span class="info-label neon-info-label"><i class="fas fa-tag neon-icon"></i> <?php esc_html_e('Pricing:', 'ai-inspiredshifter'); ?></span>
                                        <span class="info-value neon-info-value"><?php echo esc_html($pricing_type); ?></span>
                                    </li>
                                <?php endif; ?>

                                <?php if ($rating) : ?>
                                    <li class="neon-info-item">
                                        <span class="info-label neon-info-label"><i class="fas fa-star neon-icon"></i> <?php esc_html_e('Rating:', 'ai-inspiredshifter'); ?></span>
                                        <span class="info-value neon-info-value"><?php echo esc_html($rating); ?>/5</span>
                                    </li>
                                <?php endif; ?>

                                <li class="neon-info-item">
                                    <span class="info-label neon-info-label"><i class="fas fa-calendar-alt neon-icon"></i> <?php esc_html_e('Updated:', 'ai-inspiredshifter'); ?></span>
                                    <span class="info-value neon-info-value"><?php echo get_the_modified_date(); ?></span>
                                </li>
                            </ul>
                        </div>

                        <?php if ($affiliate_link) : ?>
                            <div class="tool-sidebar-widget tool-cta-widget neon-cta-widget">
                                <h3 class="neon-widget-title"><?php esc_html_e('Try This Tool', 'ai-inspiredshifter'); ?></h3>
                                <p class="neon-text"><?php esc_html_e('Ready to experience this AI tool? Click the button below to get started.', 'ai-inspiredshifter'); ?></p>
                                <?php echo do_shortcode('[affiliate_link url="' . esc_url($affiliate_link) . '" text="Try Now" class="button button-full neon-button-full"]'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="tool-sidebar-widget related-tools neon-related-tools">
                            <h3 class="neon-widget-title"><?php esc_html_e('Related Tools', 'ai-inspiredshifter'); ?></h3>
                            <?php
                            // Get related tools based on categories
                            if (!empty($categories) && !is_wp_error($categories)) {
                                $category_ids = array();
                                foreach ($categories as $category) {
                                    $category_ids[] = $category->term_id;
                                }
                                
                                $related_args = array(
                                    'post_type'      => 'ai_tool',
                                    'posts_per_page' => 3,
                                    'post__not_in'   => array(get_the_ID()),
                                    'tax_query'      => array(
                                        array(
                                            'taxonomy' => 'ai_category',
                                            'field'    => 'term_id',
                                            'terms'    => $category_ids,
                                        ),
                                    ),
                                );
                                
                                $related_tools = new WP_Query($related_args);
                                
                                if ($related_tools->have_posts()) :
                                    echo '<ul class="related-tools-list neon-related-tools-list">';
                                    while ($related_tools->have_posts()) : $related_tools->the_post();
                                        echo '<li class="neon-related-tool-item">';
                                        if (has_post_thumbnail()) {
                                            echo '<a href="' . get_permalink() . '" class="related-tool-image neon-related-tool-image">';
                                            the_post_thumbnail('thumbnail');
                                            echo '</a>';
                                        }
                                        echo '<div class="related-tool-content neon-related-tool-content">';
                                        echo '<h4 class="neon-related-tool-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h4>';
                                        
                                        // Display rating if available
                                        $related_rating = get_post_meta(get_the_ID(), 'tool_rating', true);
                                        if ($related_rating) {
                                            echo '<div class="star-rating small neon-star-rating">';
                                            $full_stars = floor($related_rating);
                                            for ($i = 0; $i < $full_stars; $i++) {
                                                echo '<i class="fas fa-star neon-icon-glow"></i>';
                                            }
                                            
                                            $half_star = $related_rating - $full_stars;
                                            if ($half_star >= 0.25 && $half_star < 0.75) {
                                                echo '<i class="fas fa-star-half-alt neon-icon-glow"></i>';
                                                $empty_stars = 5 - $full_stars - 1;
                                            } elseif ($half_star >= 0.75) {
                                                echo '<i class="fas fa-star neon-icon-glow"></i>';
                                                $empty_stars = 5 - $full_stars - 1;
                                            } else {
                                                $empty_stars = 5 - $full_stars;
                                            }
                                            
                                            for ($i = 0; $i < $empty_stars; $i++) {
                                                echo '<i class="far fa-star neon-icon-glow"></i>';
                                            }
                                            echo '</div>';
                                        }
                                        
                                        echo '</div>';
                                        echo '</li>';
                                    endwhile;
                                    echo '</ul>';
                                    wp_reset_postdata();
                                else :
                                    echo '<p class="neon-text">' . esc_html__('No related tools found.', 'ai-inspiredshifter') . '</p>';
                                endif;
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </article><!-- #post-<?php the_ID(); ?> -->

            <?php
            // If comments are open or we have at least one comment, load up the comment template.
            if (comments_open() || get_comments_number()) :
                comments_template();
            endif;
        endwhile; // End of the loop.
        ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
