const fetch = require('node-fetch');

async function generateReview(toolName, toolUrl, toolData) {
  try {
    // Use OpenRouter API to generate content
    const apiKey = process.env.OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error('OpenRouter API key is not configured');
    }

    const prompt = `Write a comprehensive review for the AI tool called "${toolName}" (${toolUrl}).
    Include the following sections:
    1. Introduction to the tool
    2. Key features and capabilities
    3. Use cases and applications
    4. Pros and cons
    5. Pricing (if available)
    6. Conclusion and recommendation

    Make the review informative, balanced, and helpful for readers considering using this tool.`;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-opus:beta',
        messages: [
          { role: 'user', content: prompt }
        ],
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenRouter API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const generatedContent = data.choices[0].message.content;

    // Format the content for WordPress
    const formattedContent = `
    <!-- wp:heading -->
    <h2>Review of ${toolName}</h2>
    <!-- /wp:heading -->

    <!-- wp:image -->
    <figure class="wp-block-image">
      <img src="data:image/jpeg;base64,${toolData}" alt="${toolName} Screenshot" />
      <figcaption>${toolName} - ${toolUrl}</figcaption>
    </figure>
    <!-- /wp:image -->

    <!-- wp:paragraph -->
    ${generatedContent.replace(/\n\n/g, '</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:paragraph -->\n<p>')}
    <!-- /wp:paragraph -->

    <!-- wp:paragraph -->
    <p>Check out <a href="${toolUrl}">${toolName}</a> to learn more.</p>
    <!-- /wp:paragraph -->
    `;

    return {
      title: `Review: ${toolName} - AI Tool Analysis`,
      content: formattedContent
    };
  } catch (error) {
    console.error('Error generating review:', error);
    throw error;
  }
}

module.exports = { generateReview };
