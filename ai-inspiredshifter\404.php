<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <section class="error-404 not-found">
            <div class="error-content">
                <div class="error-image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="120" height="120">
                        <path fill="url(#gradient)" d="M13 13h-2V7h2m0 10h-2v-2h2M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10 10 10 0 0 0 10-10A10 10 0 0 0 12 2z"/>
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#6366f1" />
                                <stop offset="100%" stop-color="#8b5cf6" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                
                <h1 class="page-title"><?php esc_html_e('404', 'ai-inspiredshifter'); ?></h1>
                <h2><?php esc_html_e('Page Not Found', 'ai-inspiredshifter'); ?></h2>
                <p><?php esc_html_e('The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.', 'ai-inspiredshifter'); ?></p>
                
                <div class="error-actions">
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="button">
                        <i class="fas fa-home"></i> <?php esc_html_e('Back to Home', 'ai-inspiredshifter'); ?>
                    </a>
                    
                    <a href="<?php echo esc_url(home_url('/ai-tools/')); ?>" class="button button-secondary">
                        <i class="fas fa-robot"></i> <?php esc_html_e('Explore AI Tools', 'ai-inspiredshifter'); ?>
                    </a>
                </div>
                
                <div class="search-form-container">
                    <h3><?php esc_html_e('Search our site:', 'ai-inspiredshifter'); ?></h3>
                    <?php get_search_form(); ?>
                </div>
            </div>
        </section><!-- .error-404 -->
    </div>
</main><!-- #main -->

<?php
get_footer();
