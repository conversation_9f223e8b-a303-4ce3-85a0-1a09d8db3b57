<?php
/**
 * Template Name: About Us Page
 *
 * @package InspiredShifter Child
 */

get_header();
?>

<div class="about-page">
    <div class="about-hero">
        <div class="container">
            <div class="about-hero-content">
                <h1 class="about-title"><?php the_title(); ?></h1>
                
                <?php if (get_field('about_subtitle')) : ?>
                    <p class="about-subtitle"><?php echo get_field('about_subtitle'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="about-mission">
        <div class="container">
            <div class="mission-content">
                <h2 class="section-title"><?php _e('Our Mission', 'inspiredshifter-child'); ?></h2>
                
                <?php if (get_field('mission_statement')) : ?>
                    <div class="mission-statement">
                        <?php echo get_field('mission_statement'); ?>
                    </div>
                <?php else : ?>
                    <div class="mission-statement">
                        <p><?php _e('At AI InspiredShifter, our mission is to help businesses and individuals navigate the rapidly evolving world of artificial intelligence tools. We provide unbiased reviews, comparisons, and insights to help you find the perfect AI solutions for your specific needs.', 'inspiredshifter-child'); ?></p>
                        <p><?php _e('We believe that AI technology should be accessible to everyone, and we\'re committed to demystifying complex tools and making them approachable for users at all technical levels.', 'inspiredshifter-child'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="about-team">
        <div class="container">
            <h2 class="section-title"><?php _e('Our Team', 'inspiredshifter-child'); ?></h2>
            
            <?php if (have_rows('team_members')) : ?>
                <div class="team-grid">
                    <?php while (have_rows('team_members')) : the_row(); 
                        $name = get_sub_field('name');
                        $position = get_sub_field('position');
                        $bio = get_sub_field('bio');
                        $photo = get_sub_field('photo');
                        $linkedin = get_sub_field('linkedin_url');
                        $twitter = get_sub_field('twitter_url');
                    ?>
                        <div class="team-member">
                            <?php if ($photo) : ?>
                                <div class="member-photo">
                                    <img src="<?php echo esc_url($photo['url']); ?>" alt="<?php echo esc_attr($name); ?>">
                                </div>
                            <?php else : ?>
                                <div class="member-photo member-photo-placeholder">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-4.987-3.744A7.966 7.966 0 0 0 12 20c1.97 0 3.773-.712 5.167-1.892A6.979 6.979 0 0 0 12.16 16a6.981 6.981 0 0 0-5.147 2.256zM5.616 16.82A8.975 8.975 0 0 1 12.16 14a8.972 8.972 0 0 1 6.362 2.634 8 8 0 1 0-12.906.187zM12 13a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill="rgba(149,164,166,1)"/></svg>
                                </div>
                            <?php endif; ?>
                            
                            <div class="member-info">
                                <h3 class="member-name"><?php echo esc_html($name); ?></h3>
                                
                                <?php if ($position) : ?>
                                    <p class="member-position"><?php echo esc_html($position); ?></p>
                                <?php endif; ?>
                                
                                <?php if ($bio) : ?>
                                    <div class="member-bio">
                                        <?php echo $bio; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($linkedin || $twitter) : ?>
                                    <div class="member-social">
                                        <?php if ($linkedin) : ?>
                                            <a href="<?php echo esc_url($linkedin); ?>" class="social-link linkedin" target="_blank" rel="noopener">
                                                <span class="screen-reader-text"><?php _e('LinkedIn', 'inspiredshifter-child'); ?></span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($twitter) : ?>
                                            <a href="<?php echo esc_url($twitter); ?>" class="social-link twitter" target="_blank" rel="noopener">
                                                <span class="screen-reader-text"><?php _e('Twitter', 'inspiredshifter-child'); ?></span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <!-- Default team members if none are added in ACF -->
                <div class="team-grid">
                    <div class="team-member">
                        <div class="member-photo member-photo-placeholder">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-4.987-3.744A7.966 7.966 0 0 0 12 20c1.97 0 3.773-.712 5.167-1.892A6.979 6.979 0 0 0 12.16 16a6.981 6.981 0 0 0-5.147 2.256zM5.616 16.82A8.975 8.975 0 0 1 12.16 14a8.972 8.972 0 0 1 6.362 2.634 8 8 0 1 0-12.906.187zM12 13a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill="rgba(149,164,166,1)"/></svg>
                        </div>
                        <div class="member-info">
                            <h3 class="member-name"><?php _e('John Smith', 'inspiredshifter-child'); ?></h3>
                            <p class="member-position"><?php _e('Founder & CEO', 'inspiredshifter-child'); ?></p>
                            <div class="member-bio">
                                <p><?php _e('John has over 10 years of experience in the tech industry and is passionate about helping businesses leverage AI tools effectively.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="team-member">
                        <div class="member-photo member-photo-placeholder">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-4.987-3.744A7.966 7.966 0 0 0 12 20c1.97 0 3.773-.712 5.167-1.892A6.979 6.979 0 0 0 12.16 16a6.981 6.981 0 0 0-5.147 2.256zM5.616 16.82A8.975 8.975 0 0 1 12.16 14a8.972 8.972 0 0 1 6.362 2.634 8 8 0 1 0-12.906.187zM12 13a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill="rgba(149,164,166,1)"/></svg>
                        </div>
                        <div class="member-info">
                            <h3 class="member-name"><?php _e('Sarah Johnson', 'inspiredshifter-child'); ?></h3>
                            <p class="member-position"><?php _e('Head of Content', 'inspiredshifter-child'); ?></p>
                            <div class="member-bio">
                                <p><?php _e('Sarah leads our content team and ensures that all tool reviews are thorough, accurate, and helpful for our readers.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="team-member">
                        <div class="member-photo member-photo-placeholder">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-4.987-3.744A7.966 7.966 0 0 0 12 20c1.97 0 3.773-.712 5.167-1.892A6.979 6.979 0 0 0 12.16 16a6.981 6.981 0 0 0-5.147 2.256zM5.616 16.82A8.975 8.975 0 0 1 12.16 14a8.972 8.972 0 0 1 6.362 2.634 8 8 0 1 0-12.906.187zM12 13a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill="rgba(149,164,166,1)"/></svg>
                        </div>
                        <div class="member-info">
                            <h3 class="member-name"><?php _e('Michael Chen', 'inspiredshifter-child'); ?></h3>
                            <p class="member-position"><?php _e('AI Technology Expert', 'inspiredshifter-child'); ?></p>
                            <div class="member-bio">
                                <p><?php _e('Michael has a background in machine learning and evaluates the technical aspects of the AI tools we review.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="about-history">
        <div class="container">
            <h2 class="section-title"><?php _e('Our Story', 'inspiredshifter-child'); ?></h2>
            
            <?php if (have_rows('history_timeline')) : ?>
                <div class="timeline">
                    <?php while (have_rows('history_timeline')) : the_row(); 
                        $year = get_sub_field('year');
                        $title = get_sub_field('title');
                        $description = get_sub_field('description');
                    ?>
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <span class="timeline-year"><?php echo esc_html($year); ?></span>
                            </div>
                            
                            <div class="timeline-content">
                                <h3 class="timeline-title"><?php echo esc_html($title); ?></h3>
                                <div class="timeline-description">
                                    <?php echo $description; ?>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <!-- Default timeline if none is added in ACF -->
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <span class="timeline-year">2022</span>
                        </div>
                        <div class="timeline-content">
                            <h3 class="timeline-title"><?php _e('The Beginning', 'inspiredshifter-child'); ?></h3>
                            <div class="timeline-description">
                                <p><?php _e('AI InspiredShifter was founded with a simple mission: to help people navigate the growing landscape of AI tools.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <span class="timeline-year">2023</span>
                        </div>
                        <div class="timeline-content">
                            <h3 class="timeline-title"><?php _e('Expanding Our Reach', 'inspiredshifter-child'); ?></h3>
                            <div class="timeline-description">
                                <p><?php _e('We expanded our team and began covering a wider range of AI tools across different categories.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <span class="timeline-year">2024</span>
                        </div>
                        <div class="timeline-content">
                            <h3 class="timeline-title"><?php _e('Where We Are Today', 'inspiredshifter-child'); ?></h3>
                            <div class="timeline-description">
                                <p><?php _e('Today, we\'re proud to be a trusted resource for thousands of users looking for the best AI tools for their specific needs.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="about-values">
        <div class="container">
            <h2 class="section-title"><?php _e('Our Values', 'inspiredshifter-child'); ?></h2>
            
            <?php if (have_rows('company_values')) : ?>
                <div class="values-grid">
                    <?php while (have_rows('company_values')) : the_row(); 
                        $value_title = get_sub_field('value_title');
                        $value_description = get_sub_field('value_description');
                        $value_icon = get_sub_field('value_icon');
                    ?>
                        <div class="value-card">
                            <?php if ($value_icon) : ?>
                                <div class="value-icon">
                                    <img src="<?php echo esc_url($value_icon['url']); ?>" alt="<?php echo esc_attr($value_title); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <h3 class="value-title"><?php echo esc_html($value_title); ?></h3>
                            
                            <?php if ($value_description) : ?>
                                <div class="value-description">
                                    <?php echo $value_description; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <!-- Default values if none are added in ACF -->
                <div class="values-grid">
                    <div class="value-card">
                        <h3 class="value-title"><?php _e('Transparency', 'inspiredshifter-child'); ?></h3>
                        <div class="value-description">
                            <p><?php _e('We believe in being completely transparent about how we review and recommend AI tools.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                    
                    <div class="value-card">
                        <h3 class="value-title"><?php _e('Accuracy', 'inspiredshifter-child'); ?></h3>
                        <div class="value-description">
                            <p><?php _e('We thoroughly test each tool we review to provide accurate and reliable information.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                    
                    <div class="value-card">
                        <h3 class="value-title"><?php _e('User-Focused', 'inspiredshifter-child'); ?></h3>
                        <div class="value-description">
                            <p><?php _e('Our recommendations are always focused on what will provide the most value to our users.', 'inspiredshifter-child'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="about-cta">
        <div class="container">
            <h2 class="cta-title"><?php _e('Want to Learn More?', 'inspiredshifter-child'); ?></h2>
            <p class="cta-text"><?php _e('Explore our collection of AI tools or get in touch with our team.', 'inspiredshifter-child'); ?></p>
            
            <div class="cta-buttons">
                <a href="<?php echo esc_url(home_url('/ai-tools/')); ?>" class="cta-button primary"><?php _e('Explore AI Tools', 'inspiredshifter-child'); ?></a>
                <a href="<?php echo esc_url(home_url('/contact-us/')); ?>" class="cta-button secondary"><?php _e('Contact Us', 'inspiredshifter-child'); ?></a>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>