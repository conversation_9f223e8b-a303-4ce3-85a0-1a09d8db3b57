<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package AI_InspiredShifter
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function ai_inspiredshifter_body_classes($classes) {
    // Adds a class of hfeed to non-singular pages.
    if (!is_singular()) {
        $classes[] = 'hfeed';
    }

    // Adds a class of no-sidebar when there is no sidebar present.
    if (!is_active_sidebar('sidebar-1')) {
        $classes[] = 'no-sidebar';
    }

    return $classes;
}
add_filter('body_class', 'ai_inspiredshifter_body_classes');

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function ai_inspiredshifter_pingback_header() {
    if (is_singular() && pings_open()) {
        printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
    }
}
add_action('wp_head', 'ai_inspiredshifter_pingback_header');

/**
 * Change the excerpt length
 */
function ai_inspiredshifter_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'ai_inspiredshifter_excerpt_length');

/**
 * Change the excerpt more string
 */
function ai_inspiredshifter_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'ai_inspiredshifter_excerpt_more');

/**
 * Modify archive title
 */
function ai_inspiredshifter_archive_title($title) {
    if (is_category()) {
        $title = single_cat_title('', false);
    } elseif (is_tag()) {
        $title = single_tag_title('', false);
    } elseif (is_author()) {
        $title = '<span class="vcard">' . get_the_author() . '</span>';
    } elseif (is_post_type_archive()) {
        $title = post_type_archive_title('', false);
    } elseif (is_tax()) {
        $title = single_term_title('', false);
    }

    return $title;
}
add_filter('get_the_archive_title', 'ai_inspiredshifter_archive_title');

/**
 * Add schema markup to the body
 */
function ai_inspiredshifter_schema_markup() {
    // Check if the current page is a single post or page
    if (is_singular('post')) {
        echo ' itemscope itemtype="https://schema.org/BlogPosting"';
    } elseif (is_singular('page')) {
        echo ' itemscope itemtype="https://schema.org/WebPage"';
    } elseif (is_singular('ai_tool')) {
        echo ' itemscope itemtype="https://schema.org/Product"';
    } elseif (is_home() || is_archive() || is_search()) {
        echo ' itemscope itemtype="https://schema.org/Blog"';
    } else {
        echo ' itemscope itemtype="https://schema.org/WebPage"';
    }
}

/**
 * Add responsive container to embeds
 */
function ai_inspiredshifter_embed_html($html) {
    return '<div class="responsive-embed">' . $html . '</div>';
}
add_filter('embed_oembed_html', 'ai_inspiredshifter_embed_html', 10, 3);
add_filter('video_embed_html', 'ai_inspiredshifter_embed_html'); // Jetpack

/**
 * Add custom image sizes
 */
function ai_inspiredshifter_image_sizes() {
    add_image_size('tool-thumbnail', 300, 200, true);
    add_image_size('featured-tool', 600, 400, true);
    add_image_size('blog-featured', 800, 450, true);
}
add_action('after_setup_theme', 'ai_inspiredshifter_image_sizes');

/**
 * Add custom query vars
 */
function ai_inspiredshifter_query_vars($vars) {
    $vars[] = 'tool_category';
    $vars[] = 'tool_feature';
    $vars[] = 'tool_pricing';
    $vars[] = 'tool_sort';
    return $vars;
}
add_filter('query_vars', 'ai_inspiredshifter_query_vars');

/**
 * Modify the main query for AI tools
 */
function ai_inspiredshifter_pre_get_posts($query) {
    if (!is_admin() && $query->is_main_query() && $query->is_post_type_archive('ai_tool')) {
        // Set posts per page
        $query->set('posts_per_page', 12);
        
        // Handle category filter
        $category = get_query_var('tool_category');
        if (!empty($category)) {
            $query->set('tax_query', array(
                array(
                    'taxonomy' => 'ai_category',
                    'field'    => 'slug',
                    'terms'    => $category,
                ),
            ));
        }
        
        // Handle feature filter
        $feature = get_query_var('tool_feature');
        if (!empty($feature)) {
            $query->set('tax_query', array(
                array(
                    'taxonomy' => 'ai_feature',
                    'field'    => 'slug',
                    'terms'    => $feature,
                ),
            ));
        }
        
        // Handle pricing filter
        $pricing = get_query_var('tool_pricing');
        if (!empty($pricing)) {
            $query->set('tax_query', array(
                array(
                    'taxonomy' => 'pricing_model',
                    'field'    => 'slug',
                    'terms'    => $pricing,
                ),
            ));
        }
        
        // Handle sorting
        $sort = get_query_var('tool_sort');
        if (!empty($sort)) {
            switch ($sort) {
                case 'title':
                    $query->set('orderby', 'title');
                    $query->set('order', 'ASC');
                    break;
                case 'rating':
                    $query->set('meta_key', 'tool_rating');
                    $query->set('orderby', 'meta_value_num');
                    $query->set('order', 'DESC');
                    break;
                case 'date':
                default:
                    $query->set('orderby', 'date');
                    $query->set('order', 'DESC');
                    break;
            }
        }
    }
    
    return $query;
}
add_action('pre_get_posts', 'ai_inspiredshifter_pre_get_posts');

/**
 * Add a custom class to the navigation menu items
 */
function ai_inspiredshifter_nav_menu_css_class($classes, $item) {
    if (in_array('current-menu-item', $classes)) {
        $classes[] = 'active';
    }
    
    return $classes;
}
add_filter('nav_menu_css_class', 'ai_inspiredshifter_nav_menu_css_class', 10, 2);

/**
 * Add a custom class to the navigation menu links
 */
function ai_inspiredshifter_nav_menu_link_attributes($atts, $item, $args) {
    if (in_array('current-menu-item', $item->classes)) {
        $atts['class'] = 'active';
    }
    
    return $atts;
}
add_filter('nav_menu_link_attributes', 'ai_inspiredshifter_nav_menu_link_attributes', 10, 3);
