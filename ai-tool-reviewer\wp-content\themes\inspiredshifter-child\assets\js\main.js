/**
 * InspiredShifter Child Theme - Main JavaScript
 */

(function($) {
    'use strict';

    // Document Ready
    $(document).ready(function() {
        // Initialize components
        initHeaderScroll();
        initMobileMenu();
        initDropdownToggle();
        initSmoothScroll();
        initToolCards();
        initFaqAccordion();
        initLazyLoading();
        initFilterSystem();
        initContactForm();
    });

    /**
     * Header scroll effect
     */
    function initHeaderScroll() {
        var header = $('.site-header');
        var scrollThreshold = 50;

        $(window).scroll(function() {
            if ($(window).scrollTop() > scrollThreshold) {
                header.addClass('scrolled');
            } else {
                header.removeClass('scrolled');
            }
        });

        // Trigger on page load
        if ($(window).scrollTop() > scrollThreshold) {
            header.addClass('scrolled');
        }
    }

    /**
     * Mobile menu toggle
     */
    function initMobileMenu() {
        $('.menu-toggle').on('click', function(e) {
            e.preventDefault();
            $('.main-navigation').toggleClass('toggled');
            $(this).attr('aria-expanded', $('.main-navigation').hasClass('toggled'));
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation').length && 
                !$(e.target).closest('.menu-toggle').length && 
                $('.main-navigation').hasClass('toggled')) {
                $('.main-navigation').removeClass('toggled');
                $('.menu-toggle').attr('aria-expanded', 'false');
            }
        });
    }

    /**
     * Dropdown toggle for mobile menu
     */
    function initDropdownToggle() {
        // Add dropdown toggle buttons to menu items with children
        $('.main-navigation .menu-item-has-children > a').after('<button class="dropdown-toggle" aria-expanded="false"><span class="screen-reader-text">Expand submenu</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg></button>');

        // Toggle submenu on click
        $('.dropdown-toggle').on('click', function(e) {
            e.preventDefault();
            $(this).toggleClass('toggled-on');
            $(this).attr('aria-expanded', $(this).hasClass('toggled-on'));
            $(this).next('.sub-menu').slideToggle(200);
        });

        // Reset mobile menu when window is resized
        $(window).on('resize', function() {
            if ($(window).width() > 1024) {
                $('.main-navigation').removeClass('toggled');
                $('.dropdown-toggle').removeClass('toggled-on');
                $('.sub-menu').removeAttr('style');
            }
        });
    }

    /**
     * Smooth scroll to anchors
     */
    function initSmoothScroll() {
        $('a[href*="#"]:not([href="#"]):not([href*="#tab-"]):not([href*="=#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                    return false;
                }
            }
        });
    }

    /**
     * Tool cards hover effects
     */
    function initToolCards() {
        $('.tool-card').each(function() {
            $(this).on('mouseenter', function() {
                $(this).addClass('hovered');
            }).on('mouseleave', function() {
                $(this).removeClass('hovered');
            });
        });
    }

    /**
     * FAQ accordion functionality
     */
    function initFaqAccordion() {
        $('.faq-question').on('click', function() {
            $(this).toggleClass('active');
            $(this).next('.faq-answer').slideToggle(300);
        });
    }

    /**
     * Lazy loading for images
     */
    function initLazyLoading() {
        // Check if browser supports Intersection Observer
        if ('IntersectionObserver' in window) {
            const lazyImages = document.querySelectorAll('img[data-src]');
            
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                        }
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            lazyImages.forEach(function(img) {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers that don't support Intersection Observer
            const lazyImages = document.querySelectorAll('img[data-src]');
            
            function lazyLoad() {
                const scrollTop = window.pageYOffset;
                
                lazyImages.forEach(function(img) {
                    if (img.offsetTop < window.innerHeight + scrollTop) {
                        img.src = img.dataset.src;
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                        }
                        img.classList.add('loaded');
                    }
                });
                
                if (lazyImages.length === 0) {
                    document.removeEventListener('scroll', lazyLoad);
                    window.removeEventListener('resize', lazyLoad);
                    window.removeEventListener('orientationchange', lazyLoad);
                }
            }
            
            document.addEventListener('scroll', lazyLoad);
            window.addEventListener('resize', lazyLoad);
            window.addEventListener('orientationchange', lazyLoad);
        }
    }

    /**
     * Filter system for AI tools
     */
    function initFilterSystem() {
        if ($('.category-filters').length) {
            $('.filter-select').on('change', function() {
                var priceFilter = $('#price-filter').val();
                var ratingFilter = $('#rating-filter').val();
                var featureFilter = $('#feature-filter').val();
                
                $('.tool-card').each(function() {
                    var $tool = $(this);
                    var toolPrice = $tool.data('price');
                    var toolRating = $tool.data('rating');
                    var toolFeatures = $tool.data('features') ? $tool.data('features').split(',') : [];
                    var showTool = true;
                    
                    // Price filter
                    if (priceFilter !== 'all' && toolPrice !== priceFilter) {
                        showTool = false;
                    }
                    
                    // Rating filter
                    if (ratingFilter !== 'all' && toolRating < ratingFilter) {
                        showTool = false;
                    }
                    
                    // Feature filter
                    if (featureFilter !== 'all' && toolFeatures.indexOf(featureFilter) === -1) {
                        showTool = false;
                    }
                    
                    if (showTool) {
                        $tool.fadeIn(300);
                    } else {
                        $tool.fadeOut(300);
                    }
                });
                
                // Show message if no tools visible
                setTimeout(function() {
                    if ($('.tool-card:visible').length === 0) {
                        if ($('.no-results').length === 0) {
                            $('.tools-grid').append('<p class="no-results">No tools match your filters. Please try different criteria.</p>');
                        }
                    } else {
                        $('.no-results').remove();
                    }
                }, 350);
            });
        }
    }

    /**
     * Contact form validation and submission
     */
    function initContactForm() {
        if ($('.contact-form').length) {
            $('.contact-form').on('submit', function(e) {
                var $form = $(this);
                var valid = true;
                
                // Simple validation
                $form.find('[required]').each(function() {
                    var $field = $(this);
                    
                    if (!$field.val().trim()) {
                        valid = false;
                        $field.addClass('error');
                        
                        if (!$field.next('.error-message').length) {
                            $field.after('<span class="error-message">This field is required</span>');
                        }
                    } else {
                        $field.removeClass('error');
                        $field.next('.error-message').remove();
                        
                        // Email validation
                        if ($field.attr('type') === 'email') {
                            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test($field.val())) {
                                valid = false;
                                $field.addClass('error');
                                
                                if (!$field.next('.error-message').length) {
                                    $field.after('<span class="error-message">Please enter a valid email address</span>');
                                }
                            }
                        }
                    }
                });
                
                // If using AJAX submission
                if (valid && $form.hasClass('ajax-form')) {
                    e.preventDefault();
                    
                    // Show loading state
                    $form.addClass('loading');
                    $form.find('.submit-button').prop('disabled', true).text('Sending...');
                    
                    // Simulate AJAX submission (replace with actual AJAX call)
                    setTimeout(function() {
                        $form.removeClass('loading');
                        $form.fadeOut(300, function() {
                            $form.after('<div class="form-success"><h3>Thank You!</h3><p>Your message has been sent. We\'ll get back to you as soon as possible.</p></div>');
                        });
                    }, 1500);
                }
            });
            
            // Real-time validation
            $('.contact-form [required]').on('blur', function() {
                var $field = $(this);
                
                if (!$field.val().trim()) {
                    $field.addClass('error');
                    
                    if (!$field.next('.error-message').length) {
                        $field.after('<span class="error-message">This field is required</span>');
                    }
                } else {
                    $field.removeClass('error');
                    $field.next('.error-message').remove();
                    
                    // Email validation
                    if ($field.attr('type') === 'email') {
                        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test($field.val())) {
                            $field.addClass('error');
                            
                            if (!$field.next('.error-message').length) {
                                $field.after('<span class="error-message">Please enter a valid email address</span>');
                            }
                        }
                    }
                }
            });
        }
    }

    // Add animation effects for hero section
    $(window).on('load', function() {
        setTimeout(function() {
            $('.hero-content').addClass('animated');
        }, 300);
    });

})(jQuery);