version: '3.8'

services:
  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: cloudflared
    restart: unless-stopped
    command: tunnel --no-autoupdate run --token YOUR_TUNNEL_TOKEN
    networks:
      - n8n-network

  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n
    restart: always
    ports:
      - "5678:5678"
    volumes:
      - B:\DockerVolumes\n8n_data:/home/<USER>/.n8n
    environment:
      - N8N_HOST=n8n.inspiredshifter.com
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_TUNNEL_URL=https://n8n.inspiredshifter.com/
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=<EMAIL>
      - N8N_BASIC_AUTH_PASSWORD=David+1990
    networks:
      - n8n-network

networks:
  n8n-network:
    driver: bridge
