<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <aside id="post-toc" class="post-toc neon-toc" aria-label="<?php esc_attr_e('Table of Contents', 'ai-inspiredshifter'); ?>">
            <h2 class="neon-toc-title"><?php esc_html_e('Contents', 'ai-inspiredshifter'); ?></h2>
            <nav>
                <ul></ul>
            </nav>
        </aside>

        <div class="content-area neon-content-area">
            <?php
            while (have_posts()) :
                the_post();
            ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('single-post neon-post'); ?>>
                    <header class="entry-header neon-entry-header">
                        <?php
                        // Display categories
                        $categories = get_the_category();
                        if (!empty($categories)) {
                            echo '<div class="entry-categories neon-categories">';
                            foreach ($categories as $category) {
                                echo '<a href="' . esc_url(get_category_link($category->term_id)) . '" class="neon-category-link">' . esc_html($category->name) . '</a>';
                            }
                            echo '</div>';
                        }
                        ?>

                        <?php the_title('<h1 class="entry-title neon-title">', '</h1>'); ?>

                        <div class="entry-meta neon-meta">
                            <span class="posted-on neon-posted-on">
                                <i class="far fa-calendar-alt neon-icon"></i>
                                <?php echo get_the_date(); ?>
                            </span>
                            <span class="posted-by neon-posted-by">
                                <i class="far fa-user neon-icon"></i>
                                <?php the_author_posts_link(); ?>
                            </span>
                            <span class="comments-link neon-comments-link">
                                <i class="far fa-comment neon-icon"></i>
                                <?php comments_popup_link(
                                    __('No Comments', 'ai-inspiredshifter'),
                                    __('1 Comment', 'ai-inspiredshifter'),
                                    __('% Comments', 'ai-inspiredshifter')
                                ); ?>
                            </span>
                        </div><!-- .entry-meta -->
                    </header><!-- .entry-header -->

                    <?php if (has_post_thumbnail()) : ?>
                        <div class="featured-image neon-featured-image">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="entry-content neon-content">
                        <?php
                        // Generate table of contents if the post has headings
                        $content = get_the_content();
                        $pattern = '/<h([2-3])(.*?)>(.*?)<\/h[2-3]>/i';
                        if (preg_match_all($pattern, $content, $matches)) {
                            echo '<div class="table-of-contents neon-toc-inline">';
                            echo '<h4 class="neon-toc-inline-title">' . __('Table of Contents', 'ai-inspiredshifter') . '</h4>';
                            echo '<ul>';
                            foreach ($matches[0] as $key => $heading) {
                                $level = $matches[1][$key];
                                $title = strip_tags($matches[3][$key]);
                                $anchor = sanitize_title($title);
                                
                                // Add ID to the heading in the content
                                $content = str_replace(
                                    $heading,
                                    '<h' . $level . $matches[2][$key] . ' id="' . $anchor . '">' . $matches[3][$key] . '</h' . $level . '>',
                                    $content
                                );
                                
                                echo '<li class="toc-level-' . $level . '"><a href="#' . $anchor . '">' . $title . '</a></li>';
                            }
                            echo '</ul>';
                            echo '</div>';
                            
                            // Output the modified content
                            echo apply_filters('the_content', $content);
                        } else {
                            the_content();
                        }
                        ?>
                    </div><!-- .entry-content -->

                    <footer class="entry-footer neon-footer">
                        <?php
                        // Display tags
                        $tags = get_the_tags();
                        if (!empty($tags)) {
                            echo '<div class="entry-tags neon-tags">';
                            echo '<span class="tags-label neon-tags-label">' . __('Tags:', 'ai-inspiredshifter') . '</span>';
                            foreach ($tags as $tag) {
                                echo '<a href="' . esc_url(get_tag_link($tag->term_id)) . '" class="neon-tag-link">' . esc_html($tag->name) . '</a>';
                            }
                            echo '</div>';
                        }
                        ?>

                        <?php echo ai_inspiredshifter_social_sharing(); ?>
                    </footer><!-- .entry-footer -->

                    <div class="author-bio neon-author-bio">
                        <div class="author-avatar neon-author-avatar">
                            <?php echo get_avatar(get_the_author_meta('ID'), 100); ?>
                        </div>
                        <div class="author-info neon-author-info">
                            <h3 class="author-name neon-author-name"><?php the_author(); ?></h3>
                            <?php if (get_the_author_meta('description')) : ?>
                                <p class="author-description neon-author-description"><?php the_author_meta('description'); ?></p>
                            <?php endif; ?>
                            <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="author-link neon-author-link">
                                <?php printf(__('View all posts by %s', 'ai-inspiredshifter'), get_the_author()); ?>
                            </a>
                        </div>
                    </div><!-- .author-bio -->
                </article><!-- #post-<?php the_ID(); ?> -->

                <?php
                // Related posts
                $categories = get_the_category();
                if (!empty($categories)) {
                    $category_ids = array();
                    foreach ($categories as $category) {
                        $category_ids[] = $category->term_id;
                    }
                    
                    $related_args = array(
                        'post_type'      => 'post',
                        'posts_per_page' => 3,
                        'post__not_in'   => array(get_the_ID()),
                        'category__in'   => $category_ids,
                    );
                    
                    $related_query = new WP_Query($related_args);
                    
                    if ($related_query->have_posts()) :
                    ?>
                        <div class="related-posts neon-related-posts">
                            <h3 class="related-title neon-related-title"><?php esc_html_e('Related Posts', 'ai-inspiredshifter'); ?></h3>
                            <div class="related-posts-grid neon-related-posts-grid">
                                <?php
                                while ($related_query->have_posts()) : $related_query->the_post();
                                ?>
                                    <div class="related-post neon-related-post">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <a href="<?php the_permalink(); ?>" class="related-thumbnail neon-related-thumbnail">
                                                <?php the_post_thumbnail('medium'); ?>
                                            </a>
                                        <?php endif; ?>
                                        <h4 class="related-post-title neon-related-post-title">
                                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                        </h4>
                                        <div class="related-post-meta neon-related-post-meta">
                                            <span class="related-post-date neon-post-date"><?php echo get_the_date(); ?></span>
                                        </div>
                                    </div>
                                <?php
                                endwhile;
                                wp_reset_postdata();
                                ?>
                            </div>
                        </div>
                    <?php
                    endif;
                }
                ?>

                <?php
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
            endwhile; // End of the loop.
            ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
