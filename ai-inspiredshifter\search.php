<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <header class="page-header">
            <h1 class="page-title">
                <?php
                /* translators: %s: search query. */
                printf(esc_html__('Search Results for: %s', 'ai-inspiredshifter'), '<span>' . get_search_query() . '</span>');
                ?>
            </h1>
        </header><!-- .page-header -->

        <div class="content-area">
            <?php if (have_posts()) : ?>
                <div class="search-results-info">
                    <?php
                    global $wp_query;
                    printf(
                        esc_html(_n(
                            'Found %1$s result for your search.',
                            'Found %1$s results for your search.',
                            $wp_query->found_posts,
                            'ai-inspiredshifter'
                        )),
                        number_format_i18n($wp_query->found_posts)
                    );
                    ?>
                </div>

                <div class="search-filters">
                    <div class="filter-buttons">
                        <button class="filter-button active" data-filter="all"><?php esc_html_e('All', 'ai-inspiredshifter'); ?></button>
                        <button class="filter-button" data-filter="post"><?php esc_html_e('Blog Posts', 'ai-inspiredshifter'); ?></button>
                        <button class="filter-button" data-filter="ai_tool"><?php esc_html_e('AI Tools', 'ai-inspiredshifter'); ?></button>
                        <button class="filter-button" data-filter="page"><?php esc_html_e('Pages', 'ai-inspiredshifter'); ?></button>
                    </div>
                </div>

                <div class="search-results-grid">
                    <?php
                    /* Start the Loop */
                    while (have_posts()) :
                        the_post();

                        /**
                         * Run the loop for the search to output the results.
                         * If you want to overload this in a child theme then include a file
                         * called content-search.php and that will be used instead.
                         */
                        get_template_part('template-parts/content', 'search');

                    endwhile;
                    ?>
                </div>

                <div class="pagination">
                    <?php
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => '<i class="fas fa-chevron-left"></i>',
                        'next_text' => '<i class="fas fa-chevron-right"></i>',
                    ));
                    ?>
                </div>

            <?php else : ?>

                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h2><?php esc_html_e('No Results Found', 'ai-inspiredshifter'); ?></h2>
                    <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'ai-inspiredshifter'); ?></p>
                    
                    <div class="search-form-container">
                        <?php get_search_form(); ?>
                    </div>
                    
                    <div class="search-suggestions">
                        <h3><?php esc_html_e('Search Suggestions:', 'ai-inspiredshifter'); ?></h3>
                        <ul>
                            <li><?php esc_html_e('Check your spelling.', 'ai-inspiredshifter'); ?></li>
                            <li><?php esc_html_e('Try more general keywords.', 'ai-inspiredshifter'); ?></li>
                            <li><?php esc_html_e('Try different keywords.', 'ai-inspiredshifter'); ?></li>
                            <li><?php esc_html_e('Try fewer keywords.', 'ai-inspiredshifter'); ?></li>
                        </ul>
                    </div>
                </div>

            <?php endif; ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
