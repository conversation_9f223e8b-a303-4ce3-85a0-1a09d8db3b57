<?php
/**
 * AI InspiredShifter Theme Customizer
 *
 * @package AI_InspiredShifter
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function ai_inspiredshifter_customize_register($wp_customize) {
    $wp_customize->get_setting('blogname')->transport         = 'postMessage';
    $wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
    $wp_customize->get_setting('header_textcolor')->transport = 'postMessage';

    if (isset($wp_customize->selective_refresh)) {
        $wp_customize->selective_refresh->add_partial(
            'blogname',
            array(
                'selector'        => '.site-title a',
                'render_callback' => 'ai_inspiredshifter_customize_partial_blogname',
            )
        );
        $wp_customize->selective_refresh->add_partial(
            'blogdescription',
            array(
                'selector'        => '.site-description',
                'render_callback' => 'ai_inspiredshifter_customize_partial_blogdescription',
            )
        );
    }

    // Theme Options Panel
    $wp_customize->add_panel('ai_inspiredshifter_theme_options', array(
        'title'    => __('Theme Options', 'ai-inspiredshifter'),
        'priority' => 130,
    ));

    // Colors Section
    $wp_customize->add_section('ai_inspiredshifter_colors', array(
        'title'    => __('Colors', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 10,
    ));

    // Primary Indigo Color
    $wp_customize->add_setting('primary_indigo_color', array(
        'default'           => '#6366f1',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_indigo_color', array(
        'label'    => __('Primary Indigo Color', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_colors',
        'settings' => 'primary_indigo_color',
    )));

    // Primary Purple Color
    $wp_customize->add_setting('primary_purple_color', array(
        'default'           => '#8b5cf6',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_purple_color', array(
        'label'    => __('Primary Purple Color', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_colors',
        'settings' => 'primary_purple_color',
    )));

    // Text Dark Color
    $wp_customize->add_setting('text_dark_color', array(
        'default'           => '#1e293b',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'text_dark_color', array(
        'label'    => __('Text Dark Color', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_colors',
        'settings' => 'text_dark_color',
    )));

    // Background Light Color
    $wp_customize->add_setting('background_light_color', array(
        'default'           => '#f8fafc',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'background_light_color', array(
        'label'    => __('Background Light Color', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_colors',
        'settings' => 'background_light_color',
    )));

    // Header Section
    $wp_customize->add_section('ai_inspiredshifter_header', array(
        'title'    => __('Header', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 20,
    ));

    // Sticky Header
    $wp_customize->add_setting('sticky_header', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('sticky_header', array(
        'label'    => __('Enable Sticky Header', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_header',
        'settings' => 'sticky_header',
        'type'     => 'checkbox',
    ));

    // Footer Section
    $wp_customize->add_section('ai_inspiredshifter_footer', array(
        'title'    => __('Footer', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 30,
    ));

    // Footer Copyright Text
    $wp_customize->add_setting('footer_copyright', array(
        'default'           => sprintf(__('© %s %s. All rights reserved.', 'ai-inspiredshifter'), date('Y'), get_bloginfo('name')),
        'sanitize_callback' => 'wp_kses_post',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control('footer_copyright', array(
        'label'    => __('Copyright Text', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_footer',
        'settings' => 'footer_copyright',
        'type'     => 'textarea',
    ));

    // Social Media Section
    $wp_customize->add_section('ai_inspiredshifter_social', array(
        'title'    => __('Social Media', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 40,
    ));

    // Twitter URL
    $wp_customize->add_setting('social_twitter', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('social_twitter', array(
        'label'    => __('Twitter URL', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_social',
        'settings' => 'social_twitter',
        'type'     => 'url',
    ));

    // Facebook URL
    $wp_customize->add_setting('social_facebook', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('social_facebook', array(
        'label'    => __('Facebook URL', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_social',
        'settings' => 'social_facebook',
        'type'     => 'url',
    ));

    // LinkedIn URL
    $wp_customize->add_setting('social_linkedin', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('social_linkedin', array(
        'label'    => __('LinkedIn URL', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_social',
        'settings' => 'social_linkedin',
        'type'     => 'url',
    ));

    // Instagram URL
    $wp_customize->add_setting('social_instagram', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('social_instagram', array(
        'label'    => __('Instagram URL', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_social',
        'settings' => 'social_instagram',
        'type'     => 'url',
    ));

    // Pinterest URL
    $wp_customize->add_setting('social_pinterest', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('social_pinterest', array(
        'label'    => __('Pinterest URL', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_social',
        'settings' => 'social_pinterest',
        'type'     => 'url',
    ));

    // Blog Section
    $wp_customize->add_section('ai_inspiredshifter_blog', array(
        'title'    => __('Blog', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 50,
    ));

    // Show Featured Image
    $wp_customize->add_setting('blog_show_featured_image', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('blog_show_featured_image', array(
        'label'    => __('Show Featured Image', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_blog',
        'settings' => 'blog_show_featured_image',
        'type'     => 'checkbox',
    ));

    // Show Author Bio
    $wp_customize->add_setting('blog_show_author_bio', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('blog_show_author_bio', array(
        'label'    => __('Show Author Bio', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_blog',
        'settings' => 'blog_show_author_bio',
        'type'     => 'checkbox',
    ));

    // Show Related Posts
    $wp_customize->add_setting('blog_show_related_posts', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('blog_show_related_posts', array(
        'label'    => __('Show Related Posts', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_blog',
        'settings' => 'blog_show_related_posts',
        'type'     => 'checkbox',
    ));

    // Show Social Sharing
    $wp_customize->add_setting('blog_show_social_sharing', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('blog_show_social_sharing', array(
        'label'    => __('Show Social Sharing', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_blog',
        'settings' => 'blog_show_social_sharing',
        'type'     => 'checkbox',
    ));

    // AI Tools Section
    $wp_customize->add_section('ai_inspiredshifter_tools', array(
        'title'    => __('AI Tools', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 60,
    ));

    // Tools Per Page
    $wp_customize->add_setting('tools_per_page', array(
        'default'           => 12,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('tools_per_page', array(
        'label'    => __('Tools Per Page', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_tools',
        'settings' => 'tools_per_page',
        'type'     => 'number',
        'input_attrs' => array(
            'min' => 4,
            'max' => 24,
            'step' => 4,
        ),
    ));

    // Show Tool Rating
    $wp_customize->add_setting('tools_show_rating', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('tools_show_rating', array(
        'label'    => __('Show Tool Rating', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_tools',
        'settings' => 'tools_show_rating',
        'type'     => 'checkbox',
    ));

    // Show Tool Categories
    $wp_customize->add_setting('tools_show_categories', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('tools_show_categories', array(
        'label'    => __('Show Tool Categories', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_tools',
        'settings' => 'tools_show_categories',
        'type'     => 'checkbox',
    ));

    // Show Tool Pricing
    $wp_customize->add_setting('tools_show_pricing', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('tools_show_pricing', array(
        'label'    => __('Show Tool Pricing', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_tools',
        'settings' => 'tools_show_pricing',
        'type'     => 'checkbox',
    ));

    // Performance Section
    $wp_customize->add_section('ai_inspiredshifter_performance', array(
        'title'    => __('Performance', 'ai-inspiredshifter'),
        'panel'    => 'ai_inspiredshifter_theme_options',
        'priority' => 70,
    ));

    // Enable Lazy Loading
    $wp_customize->add_setting('enable_lazy_loading', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_lazy_loading', array(
        'label'    => __('Enable Lazy Loading for Images', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_performance',
        'settings' => 'enable_lazy_loading',
        'type'     => 'checkbox',
    ));

    // Enable Dark Mode
    $wp_customize->add_setting('enable_dark_mode', array(
        'default'           => true,
        'sanitize_callback' => 'ai_inspiredshifter_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_dark_mode', array(
        'label'    => __('Enable Dark Mode Toggle', 'ai-inspiredshifter'),
        'section'  => 'ai_inspiredshifter_performance',
        'settings' => 'enable_dark_mode',
        'type'     => 'checkbox',
    ));
}
add_action('customize_register', 'ai_inspiredshifter_customize_register');

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function ai_inspiredshifter_customize_partial_blogname() {
    bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function ai_inspiredshifter_customize_partial_blogdescription() {
    bloginfo('description');
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function ai_inspiredshifter_customize_preview_js() {
    wp_enqueue_script('ai-inspiredshifter-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array('customize-preview'), AI_INSPIREDSHIFTER_VERSION, true);
}
add_action('customize_preview_init', 'ai_inspiredshifter_customize_preview_js');

/**
 * Sanitize checkbox values
 */
function ai_inspiredshifter_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Generate CSS from customizer settings
 */
function ai_inspiredshifter_customizer_css() {
    $primary_indigo = get_theme_mod('primary_indigo_color', '#6366f1');
    $primary_purple = get_theme_mod('primary_purple_color', '#8b5cf6');
    $text_dark = get_theme_mod('text_dark_color', '#1e293b');
    $background_light = get_theme_mod('background_light_color', '#f8fafc');
    
    $css = ':root {
        --primary-indigo: ' . esc_attr($primary_indigo) . ';
        --primary-purple: ' . esc_attr($primary_purple) . ';
        --primary-gradient: linear-gradient(90deg, ' . esc_attr($primary_indigo) . ', ' . esc_attr($primary_purple) . ');
        --text-dark: ' . esc_attr($text_dark) . ';
        --background-light: ' . esc_attr($background_light) . ';
    }';
    
    return $css;
}

/**
 * Output generated CSS to wp_head
 */
function ai_inspiredshifter_customizer_head_styles() {
    $css = ai_inspiredshifter_customizer_css();
    
    if (!empty($css)) {
        echo '<style type="text/css">' . $css . '</style>';
    }
}
add_action('wp_head', 'ai_inspiredshifter_customizer_head_styles');
