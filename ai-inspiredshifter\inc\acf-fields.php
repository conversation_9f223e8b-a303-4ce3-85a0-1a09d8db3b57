<?php
/**
 * ACF Fields for AI InspiredShifter Theme
 *
 * @package AI_InspiredShifter
 */

/**
 * Register ACF fields
 */
function ai_inspiredshifter_register_acf_fields() {
    // AI Tool Fields
    acf_add_local_field_group(array(
        'key' => 'group_ai_tool_details',
        'title' => 'AI Tool Details',
        'fields' => array(
            array(
                'key' => 'field_tool_short_description',
                'label' => 'Short Description',
                'name' => 'tool_short_description',
                'type' => 'textarea',
                'instructions' => 'Enter a short description of the AI tool (max 160 characters).',
                'required' => 1,
                'maxlength' => 160,
                'rows' => 3,
            ),
            array(
                'key' => 'field_tool_rating',
                'label' => 'Rating',
                'name' => 'tool_rating',
                'type' => 'number',
                'instructions' => 'Enter a rating from 1 to 5 (decimals allowed, e.g. 4.5).',
                'required' => 1,
                'min' => 1,
                'max' => 5,
                'step' => 0.1,
            ),
            array(
                'key' => 'field_tool_pricing_type',
                'label' => 'Pricing Type',
                'name' => 'tool_pricing_type',
                'type' => 'select',
                'instructions' => 'Select the pricing type for this tool.',
                'required' => 1,
                'choices' => array(
                    'Free' => 'Free',
                    'Freemium' => 'Freemium',
                    'Paid' => 'Paid',
                    'Subscription' => 'Subscription',
                    'One-time Purchase' => 'One-time Purchase',
                ),
                'default_value' => 'Freemium',
                'return_format' => 'value',
            ),
            array(
                'key' => 'field_tool_starting_price',
                'label' => 'Starting Price',
                'name' => 'tool_starting_price',
                'type' => 'text',
                'instructions' => 'Enter the starting price (e.g. $9.99/mo, Free, etc.).',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_tool_pricing_type',
                            'operator' => '!=',
                            'value' => 'Free',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_tool_pricing_plans',
                'label' => 'Pricing Plans',
                'name' => 'tool_pricing_plans',
                'type' => 'wysiwyg',
                'instructions' => 'Enter detailed pricing plans information.',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_tool_pricing_type',
                            'operator' => '!=',
                            'value' => 'Free',
                        ),
                    ),
                ),
                'tabs' => 'all',
                'toolbar' => 'full',
                'media_upload' => 0,
            ),
            array(
                'key' => 'field_tool_features',
                'label' => 'Features',
                'name' => 'tool_features',
                'type' => 'textarea',
                'instructions' => 'Enter the features of the tool, one per line.',
                'required' => 1,
                'rows' => 10,
            ),
            array(
                'key' => 'field_tool_pros',
                'label' => 'Pros',
                'name' => 'tool_pros',
                'type' => 'textarea',
                'instructions' => 'Enter the pros of the tool, one per line.',
                'required' => 1,
                'rows' => 6,
            ),
            array(
                'key' => 'field_tool_cons',
                'label' => 'Cons',
                'name' => 'tool_cons',
                'type' => 'textarea',
                'instructions' => 'Enter the cons of the tool, one per line.',
                'required' => 1,
                'rows' => 6,
            ),
            array(
                'key' => 'field_tool_screenshots',
                'label' => 'Screenshots',
                'name' => 'tool_screenshots',
                'type' => 'gallery',
                'instructions' => 'Upload screenshots of the tool.',
                'required' => 0,
                'min' => 0,
                'max' => 10,
                'insert' => 'append',
                'library' => 'all',
                'min_width' => 0,
                'min_height' => 0,
                'min_size' => 0,
                'max_width' => 0,
                'max_height' => 0,
                'max_size' => 0,
                'mime_types' => 'jpg, jpeg, png, gif',
            ),
            array(
                'key' => 'field_tool_affiliate_link',
                'label' => 'Affiliate Link',
                'name' => 'tool_affiliate_link',
                'type' => 'url',
                'instructions' => 'Enter the affiliate link for this tool.',
                'required' => 0,
            ),
            array(
                'key' => 'field_tool_featured',
                'label' => 'Featured Tool',
                'name' => 'featured_tool',
                'type' => 'true_false',
                'instructions' => 'Mark this tool as featured to display it on the homepage.',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
            ),
            array(
                'key' => 'field_tool_popular',
                'label' => 'Popular Tool',
                'name' => 'popular_tool',
                'type' => 'true_false',
                'instructions' => 'Mark this tool as popular.',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'ai_tool',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

    // AI Category Fields
    acf_add_local_field_group(array(
        'key' => 'group_ai_category_details',
        'title' => 'AI Category Details',
        'fields' => array(
            array(
                'key' => 'field_category_icon',
                'label' => 'Category Icon',
                'name' => 'category_icon',
                'type' => 'text',
                'instructions' => 'Enter a Font Awesome icon class (e.g. fa-robot).',
                'required' => 0,
            ),
            array(
                'key' => 'field_category_featured',
                'label' => 'Featured Category',
                'name' => 'featured_category',
                'type' => 'true_false',
                'instructions' => 'Mark this category as featured to display it on the homepage.',
                'required' => 0,
                'default_value' => 0,
                'ui' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'ai_category',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

    // Homepage Fields
    acf_add_local_field_group(array(
        'key' => 'group_homepage_sections',
        'title' => 'Homepage Sections',
        'fields' => array(
            array(
                'key' => 'field_hero_title',
                'label' => 'Hero Title',
                'name' => 'hero_title',
                'type' => 'text',
                'instructions' => 'Enter the hero section title.',
                'required' => 0,
                'default_value' => 'Discover the Future of AI Tools',
            ),
            array(
                'key' => 'field_hero_description',
                'label' => 'Hero Description',
                'name' => 'hero_description',
                'type' => 'textarea',
                'instructions' => 'Enter the hero section description.',
                'required' => 0,
                'default_value' => 'Explore cutting-edge artificial intelligence tools and resources to enhance your productivity and creativity.',
                'rows' => 3,
            ),
            array(
                'key' => 'field_hero_button_text',
                'label' => 'Hero Button Text',
                'name' => 'hero_button_text',
                'type' => 'text',
                'instructions' => 'Enter the hero button text.',
                'required' => 0,
                'default_value' => 'Explore AI Tools',
            ),
            array(
                'key' => 'field_hero_button_url',
                'label' => 'Hero Button URL',
                'name' => 'hero_button_url',
                'type' => 'url',
                'instructions' => 'Enter the hero button URL.',
                'required' => 0,
            ),
            array(
                'key' => 'field_offer_title',
                'label' => 'What We Offer Title',
                'name' => 'offer_title',
                'type' => 'text',
                'instructions' => 'Enter the "What We Offer" section title.',
                'required' => 0,
                'default_value' => 'What We Offer',
            ),
            array(
                'key' => 'field_offer_description',
                'label' => 'What We Offer Description',
                'name' => 'offer_description',
                'type' => 'textarea',
                'instructions' => 'Enter the "What We Offer" section description.',
                'required' => 0,
                'default_value' => 'Comprehensive resources to help you navigate the world of artificial intelligence.',
                'rows' => 3,
            ),
            array(
                'key' => 'field_offer_cards',
                'label' => 'Offer Cards',
                'name' => 'offer_cards',
                'type' => 'repeater',
                'instructions' => 'Add cards to the "What We Offer" section.',
                'required' => 0,
                'min' => 0,
                'max' => 3,
                'layout' => 'block',
                'button_label' => 'Add Card',
                'sub_fields' => array(
                    array(
                        'key' => 'field_offer_card_icon',
                        'label' => 'Icon',
                        'name' => 'icon',
                        'type' => 'text',
                        'instructions' => 'Enter a Font Awesome icon class (e.g. fa-search).',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_offer_card_title',
                        'label' => 'Title',
                        'name' => 'title',
                        'type' => 'text',
                        'instructions' => 'Enter the card title.',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_offer_card_description',
                        'label' => 'Description',
                        'name' => 'description',
                        'type' => 'textarea',
                        'instructions' => 'Enter the card description.',
                        'required' => 1,
                        'rows' => 3,
                    ),
                ),
            ),
            array(
                'key' => 'field_tools_title',
                'label' => 'Top AI Tools Title',
                'name' => 'tools_title',
                'type' => 'text',
                'instructions' => 'Enter the "Top AI Tools" section title.',
                'required' => 0,
                'default_value' => 'Top AI Tools',
            ),
            array(
                'key' => 'field_tools_description',
                'label' => 'Top AI Tools Description',
                'name' => 'tools_description',
                'type' => 'textarea',
                'instructions' => 'Enter the "Top AI Tools" section description.',
                'required' => 0,
                'default_value' => 'Discover the most popular and highly-rated AI tools in our collection.',
                'rows' => 3,
            ),
            array(
                'key' => 'field_newsletter_title',
                'label' => 'Newsletter Title',
                'name' => 'newsletter_title',
                'type' => 'text',
                'instructions' => 'Enter the newsletter section title.',
                'required' => 0,
                'default_value' => 'Stay Updated with AI Trends',
            ),
            array(
                'key' => 'field_newsletter_description',
                'label' => 'Newsletter Description',
                'name' => 'newsletter_description',
                'type' => 'textarea',
                'instructions' => 'Enter the newsletter section description.',
                'required' => 0,
                'default_value' => 'Subscribe to our newsletter to receive the latest updates on AI tools, resources, and insights.',
                'rows' => 3,
            ),
            array(
                'key' => 'field_newsletter_form',
                'label' => 'Newsletter Form Shortcode',
                'name' => 'newsletter_form',
                'type' => 'text',
                'instructions' => 'Enter the newsletter form shortcode.',
                'required' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'default',
                ),
                array(
                    'param' => 'page_type',
                    'operator' => '==',
                    'value' => 'front_page',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

    // About Page Fields
    acf_add_local_field_group(array(
        'key' => 'group_about_page',
        'title' => 'About Page',
        'fields' => array(
            array(
                'key' => 'field_about_subtitle',
                'label' => 'Subtitle',
                'name' => 'about_subtitle',
                'type' => 'text',
                'instructions' => 'Enter a subtitle for the about page.',
                'required' => 0,
            ),
            array(
                'key' => 'field_about_mission',
                'label' => 'Mission Statement',
                'name' => 'about_mission',
                'type' => 'textarea',
                'instructions' => 'Enter your mission statement.',
                'required' => 0,
                'rows' => 4,
            ),
            array(
                'key' => 'field_about_team',
                'label' => 'Team Members',
                'name' => 'about_team',
                'type' => 'repeater',
                'instructions' => 'Add team members.',
                'required' => 0,
                'min' => 0,
                'max' => 0,
                'layout' => 'block',
                'button_label' => 'Add Team Member',
                'sub_fields' => array(
                    array(
                        'key' => 'field_team_photo',
                        'label' => 'Photo',
                        'name' => 'photo',
                        'type' => 'image',
                        'instructions' => 'Upload a photo of the team member.',
                        'required' => 0,
                        'return_format' => 'id',
                        'preview_size' => 'medium',
                        'library' => 'all',
                    ),
                    array(
                        'key' => 'field_team_name',
                        'label' => 'Name',
                        'name' => 'name',
                        'type' => 'text',
                        'instructions' => 'Enter the team member\'s name.',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_team_position',
                        'label' => 'Position',
                        'name' => 'position',
                        'type' => 'text',
                        'instructions' => 'Enter the team member\'s position.',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_team_bio',
                        'label' => 'Bio',
                        'name' => 'bio',
                        'type' => 'textarea',
                        'instructions' => 'Enter a short bio for the team member.',
                        'required' => 0,
                        'rows' => 4,
                    ),
                    array(
                        'key' => 'field_team_social',
                        'label' => 'Social Links',
                        'name' => 'social',
                        'type' => 'repeater',
                        'instructions' => 'Add social media links.',
                        'required' => 0,
                        'min' => 0,
                        'max' => 0,
                        'layout' => 'table',
                        'button_label' => 'Add Social Link',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_social_platform',
                                'label' => 'Platform',
                                'name' => 'platform',
                                'type' => 'select',
                                'instructions' => 'Select the social media platform.',
                                'required' => 1,
                                'choices' => array(
                                    'twitter' => 'Twitter',
                                    'facebook' => 'Facebook',
                                    'linkedin' => 'LinkedIn',
                                    'instagram' => 'Instagram',
                                    'github' => 'GitHub',
                                ),
                                'default_value' => 'twitter',
                                'return_format' => 'value',
                            ),
                            array(
                                'key' => 'field_social_url',
                                'label' => 'URL',
                                'name' => 'url',
                                'type' => 'url',
                                'instructions' => 'Enter the social media URL.',
                                'required' => 1,
                            ),
                        ),
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'default',
                ),
                array(
                    'param' => 'page',
                    'operator' => '==',
                    'value' => '2', // Assuming page ID 2 is the About page
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));

    // Contact Page Fields
    acf_add_local_field_group(array(
        'key' => 'group_contact_page',
        'title' => 'Contact Page',
        'fields' => array(
            array(
                'key' => 'field_contact_subtitle',
                'label' => 'Subtitle',
                'name' => 'contact_subtitle',
                'type' => 'text',
                'instructions' => 'Enter a subtitle for the contact page.',
                'required' => 0,
            ),
            array(
                'key' => 'field_contact_info',
                'label' => 'Contact Information',
                'name' => 'contact_info',
                'type' => 'group',
                'instructions' => 'Enter your contact information.',
                'required' => 0,
                'layout' => 'block',
                'sub_fields' => array(
                    array(
                        'key' => 'field_contact_email',
                        'label' => 'Email',
                        'name' => 'email',
                        'type' => 'email',
                        'instructions' => 'Enter your email address.',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'field_contact_phone',
                        'label' => 'Phone',
                        'name' => 'phone',
                        'type' => 'text',
                        'instructions' => 'Enter your phone number.',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'field_contact_address',
                        'label' => 'Address',
                        'name' => 'address',
                        'type' => 'textarea',
                        'instructions' => 'Enter your address.',
                        'required' => 0,
                        'rows' => 3,
                    ),
                ),
            ),
            array(
                'key' => 'field_contact_form',
                'label' => 'Contact Form Shortcode',
                'name' => 'contact_form',
                'type' => 'text',
                'instructions' => 'Enter the contact form shortcode.',
                'required' => 0,
            ),
            array(
                'key' => 'field_contact_map',
                'label' => 'Map Embed Code',
                'name' => 'contact_map',
                'type' => 'textarea',
                'instructions' => 'Enter the Google Maps embed code.',
                'required' => 0,
                'rows' => 4,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'default',
                ),
                array(
                    'param' => 'page',
                    'operator' => '==',
                    'value' => '3', // Assuming page ID 3 is the Contact page
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
}

// Call the function to register ACF fields
ai_inspiredshifter_register_acf_fields();
