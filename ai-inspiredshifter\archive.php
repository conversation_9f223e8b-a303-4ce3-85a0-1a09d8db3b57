<?php
/**
 * The template for displaying archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <header class="page-header neon-page-header">
            <?php
            the_archive_title('<h1 class="page-title neon-title">', '</h1>');
            the_archive_description('<div class="archive-description neon-description">', '</div>');
            ?>
        </header><!-- .page-header -->

        <div class="content-area neon-content-area">
            <?php if (have_posts()) : ?>

                <nav class="category-buttons neon-category-buttons" aria-label="<?php esc_attr_e('Category filter', 'ai-inspiredshifter'); ?>">
                    <?php
                    $categories = get_categories(array(
                        'orderby' => 'name',
                        'order'   => 'ASC'
                    ));
                    foreach ($categories as $category) {
                        // Use data-category attribute for AJAX filtering
                        echo '<button type="button" class="category-button neon-category-button" data-category="' . esc_attr($category->slug) . '" aria-pressed="false">' . esc_html($category->name) . '</button>';
                    }
                    ?>
                </nav>

                <div class="blog-grid neon-blog-grid" aria-live="polite" aria-relevant="additions removals">
                    <?php
                    /* Start the Loop */
                    while (have_posts()) :
                        the_post();

                        /*
                         * Include the Post-Type-specific template for the content.
                         * If you want to override this in a child theme, then include a file
                         * called content-___.php (where ___ is the Post Type name) and that will be used instead.
                         */
                        get_template_part('template-parts/content', get_post_type());

                    endwhile;
                    ?>
                </div>

                <div class="pagination neon-pagination">
                    <?php
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => '<i class="fas fa-chevron-left neon-icon"></i>',
                        'next_text' => '<i class="fas fa-chevron-right neon-icon"></i>',
                    ));
                    ?>
                </div>

            <?php else : ?>

                <?php get_template_part('template-parts/content', 'none'); ?>

            <?php endif; ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
