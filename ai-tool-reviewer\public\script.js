document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const toolForm = document.getElementById('tool-form');
    const toolName = document.getElementById('tool-name');
    const toolUrl = document.getElementById('tool-url');
    const generateBtn = document.getElementById('generate-btn');
    
    const inputSection = document.getElementById('input-section');
    const loadingSection = document.getElementById('loading-section');
    const previewSection = document.getElementById('preview-section');
    const editSection = document.getElementById('edit-section');
    const resultSection = document.getElementById('result-section');
    
    const statusMessage = document.getElementById('status-message');
    const toolScreenshot = document.getElementById('tool-screenshot');
    const contentPreview = document.getElementById('content-preview');
    
    const editBtn = document.getElementById('edit-btn');
    const publishBtn = document.getElementById('publish-btn');
    const postTitle = document.getElementById('post-title');
    const postContent = document.getElementById('post-content');
    const updateBtn = document.getElementById('update-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    
    const resultMessage = document.getElementById('result-message');
    const newReviewBtn = document.getElementById('new-review-btn');
    
    // Store review data
    let reviewData = {
        toolName: '',
        toolUrl: '',
        screenshot: '',
        title: '',
        content: ''
    };
    
    // Event Listeners
    toolForm.addEventListener('submit', handleFormSubmit);
    editBtn.addEventListener('click', showEditForm);
    publishBtn.addEventListener('click', publishToWordPress);
    updateBtn.addEventListener('click', updateContent);
    cancelEditBtn.addEventListener('click', cancelEdit);
    newReviewBtn.addEventListener('click', resetForm);
    
    // Handle form submission
    async function handleFormSubmit(e) {
        e.preventDefault();
        
        // Validate form
        if (!toolName.value || !toolUrl.value) {
            alert('Please enter both tool name and URL');
            return;
        }
        
        // Update UI
        inputSection.classList.add('hidden');
        loadingSection.classList.remove('hidden');
        statusMessage.textContent = 'Capturing screenshot...';
        
        try {
            // Step 1: Capture screenshot
            const screenshotResult = await captureScreenshot(toolName.value, toolUrl.value);
            
            if (!screenshotResult.success) {
                throw new Error(screenshotResult.error || 'Failed to capture screenshot');
            }
            
            reviewData.toolName = screenshotResult.toolName;
            reviewData.toolUrl = screenshotResult.toolUrl;
            reviewData.screenshot = screenshotResult.screenshot;
            
            // Update status
            statusMessage.textContent = 'Generating review content...';
            
            // Step 2: Generate review content
            const contentResult = await generateContent(
                reviewData.toolName,
                reviewData.toolUrl,
                reviewData.screenshot
            );
            
            if (!contentResult.success) {
                throw new Error(contentResult.error || 'Failed to generate content');
            }
            
            reviewData.title = contentResult.content.title;
            reviewData.content = contentResult.content.content;
            
            // Update preview
            toolScreenshot.src = `data:image/jpeg;base64,${reviewData.screenshot}`;
            contentPreview.innerHTML = `<h3>${reviewData.title}</h3>${reviewData.content}`;
            
            // Update UI
            loadingSection.classList.add('hidden');
            previewSection.classList.remove('hidden');
            
        } catch (error) {
            console.error('Error:', error);
            statusMessage.textContent = `Error: ${error.message}`;
            setTimeout(() => {
                loadingSection.classList.add('hidden');
                inputSection.classList.remove('hidden');
            }, 3000);
        }
    }
    
    // Show edit form
    function showEditForm() {
        postTitle.value = reviewData.title;
        postContent.value = reviewData.content;
        
        previewSection.classList.add('hidden');
        editSection.classList.remove('hidden');
    }
    
    // Update content after editing
    function updateContent() {
        reviewData.title = postTitle.value;
        reviewData.content = postContent.value;
        
        contentPreview.innerHTML = `<h3>${reviewData.title}</h3>${reviewData.content}`;
        
        editSection.classList.add('hidden');
        previewSection.classList.remove('hidden');
    }
    
    // Cancel editing
    function cancelEdit() {
        editSection.classList.add('hidden');
        previewSection.classList.remove('hidden');
    }
    
    // Publish to WordPress
    async function publishToWordPress() {
        // Update UI
        previewSection.classList.add('hidden');
        loadingSection.classList.remove('hidden');
        statusMessage.textContent = 'Publishing to WordPress...';
        
        try {
            const publishResult = await fetch('/api/publish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: reviewData.title,
                    content: reviewData.content
                })
            });
            
            const data = await publishResult.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Failed to publish to WordPress');
            }
            
            // Update result message
            resultMessage.innerHTML = `
                <p>Successfully published to WordPress!</p>
                <p>Post ID: ${data.postId}</p>
                <p>View post: <a href="${data.postUrl}" target="_blank">${data.postUrl}</a></p>
            `;
            
            // Update UI
            loadingSection.classList.add('hidden');
            resultSection.classList.remove('hidden');
            
        } catch (error) {
            console.error('Error:', error);
            resultMessage.innerHTML = `
                <p>Error publishing to WordPress:</p>
                <p>${error.message}</p>
                <p>Please try again or check your WordPress credentials.</p>
            `;
            
            loadingSection.classList.add('hidden');
            resultSection.classList.remove('hidden');
        }
    }
    
    // Reset form for new review
    function resetForm() {
        toolName.value = '';
        toolUrl.value = '';
        
        reviewData = {
            toolName: '',
            toolUrl: '',
            screenshot: '',
            title: '',
            content: ''
        };
        
        resultSection.classList.add('hidden');
        inputSection.classList.remove('hidden');
    }
    
    // API Functions
    async function captureScreenshot(toolName, toolUrl) {
        try {
            const response = await fetch('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ toolName, toolUrl })
            });
            
            return await response.json();
        } catch (error) {
            console.error('Error capturing screenshot:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async function generateContent(toolName, toolUrl, screenshot) {
        try {
            const response = await fetch('/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ toolName, toolUrl, toolData: screenshot })
            });
            
            return await response.json();
        } catch (error) {
            console.error('Error generating content:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
});
