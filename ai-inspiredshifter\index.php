<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <div class="content-area neon-content-area">
            <?php if (have_posts()) : ?>
                <div class="blog-grid neon-blog-grid" aria-live="polite" aria-relevant="additions removals">
                    <?php
                    /* Start the Loop */
                    while (have_posts()) :
                        the_post();

                        /*
                         * Include the Post-Type-specific template for the content.
                         * If you want to override this in a child theme, then include a file
                         * called content-___.php (where ___ is the Post Type name) and that will be used instead.
                         */
                        get_template_part('template-parts/content', get_post_type());

                    endwhile;
                    ?>
                </div>

                <div class="pagination neon-pagination">
                    <?php
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => '<i class="fas fa-chevron-left neon-icon"></i>',
                        'next_text' => '<i class="fas fa-chevron-right neon-icon"></i>',
                    ));
                    ?>
                </div>

            <?php else : ?>

                <?php get_template_part('template-parts/content', 'none'); ?>

            <?php endif; ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main><!-- #main -->

<?php
get_footer();
