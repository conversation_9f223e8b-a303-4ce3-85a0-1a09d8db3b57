const puppeteer = require('puppeteer');

async function captureScreenshot(url) {
  let browser;
  try {
    browser = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
      headless: true
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });

    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });

    // Scroll to capture full page content
    await autoScroll(page);

    // Capture screenshot
    const screenshot = await page.screenshot({
      fullPage: true,
      type: 'jpeg',
      quality: 80
    });

    return {
      success: true,
      image: screenshot.toString('base64')
    };
  } catch (error) {
    console.error('Error capturing screenshot:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (browser) await browser.close();
  }
}

// Auto-scroll function for capturing full page
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        if (totalHeight >= scrollHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}

module.exports = { captureScreenshot };
