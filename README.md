# AI InspiredShifter Theme - Dark Cyberpunk Neon Edition

## Introduction

AI InspiredShifter is a modern WordPress theme designed for AI tool directories, reviews, and tech blogs. This theme features a striking dark cyberpunk neon design with glowing elements, interactive particles, and dynamic content displays. The theme is optimized for showcasing AI tools and resources with comprehensive filtering and categorization capabilities.

---

## Key Features

- **Dark Cyberpunk Neon Design**: Featuring a sleek dark background (#0a0a0a, #222222) with vibrant neon accents (#FFA500, #BF00FF, #00FFFF)
- **Interactive UI Elements**: Neon glowing buttons, links, and icons with hover effects and microinteractions
- **Particle Effect Hero Section**: Dynamic animated particles in hero areas using custom JavaScript
- **Responsive & Mobile-Friendly**: Fully responsive design with mobile menu and optimized layouts
- **Custom Post Types**: Dedicated "AI Tools" post type with taxonomies for categories, features, and pricing models
- **AJAX Filtering**: Dynamic filtering and sorting without page reloads
- **Custom Shortcodes**: For ratings, affiliate links, and social sharing
- **Block Editor Compatible**: Full support for the WordPress block editor
- **Elementor Compatible**: Enhanced support for the Elementor page builder

---

## Installation

1. **Upload Theme**:
   - Upload the `ai-inspiredshifter` theme folder to your WordPress `wp-content/themes/` directory
   - Alternatively, zip the folder and upload it via Appearance > Themes > Add New > Upload Theme

2. **Activate Theme**:
   - Log in to your WordPress admin dashboard
   - Navigate to **Appearance > Themes**
   - Locate and activate the **AI InspiredShifter** theme

3. **Required Plugins**:
   - The theme works best with the following plugins:
     - **Font Awesome 5**: For neon icons and glyphs
     - **Advanced Custom Fields (ACF)**: For enhanced AI Tool metadata
     - **Optional**: Elementor for advanced page building

---

## Import Demo Content

1. **Access WordPress Importer**:
   - Navigate to **Tools > Import** in your WordPress dashboard
   - Install and activate the WordPress Importer plugin if prompted

2. **Import Demo XML**:
   - Choose the `ai-inspiredshifter-demo-content.xml` file provided with the theme
   - Upload and import the file
   - Check "Download and import file attachments" to include images

3. **Content Organization**:
   - The demo includes:
     - Sample blog posts
     - AI Tool entries with categories, features, and ratings
     - Pages including Home, About, Contact
     - Pre-configured menus and widgets

---

## Theme Configuration

### Appearance Customization

1. **Site Identity**:
   - Navigate to **Appearance > Customize > Site Identity**
   - Upload a logo that works well with the dark background
   - Set your site title and tagline

2. **Menus Setup**:
   - Go to **Appearance > Menus**
   - Create a primary menu and assign to "Primary Menu" location
   - Optionally create a footer menu for "Footer Menu" location

3. **Widget Areas**:
   - Configure widgets in **Appearance > Widgets**
   - The theme includes:
     - Blog Sidebar
     - Footer Columns (3)

### Homepage Setup

1. **Static Homepage**:
   - Go to **Settings > Reading**
   - Select "A static page" for your homepage
   - Choose the imported "Home" page or create a new one

2. **Hero Section Configuration**:
   - Edit your homepage
   - The particle effect automatically initializes in sections with class `hero-section` or `neon-hero`
   - Adjust colors via theme customizer or CSS variables

### AI Tools Directory

1. **Managing AI Tools**:
   - Access via **AI Tools** menu in the WordPress admin
   - Add new tools with:
     - Featured image (logo)
     - Description
     - Custom fields for ratings, pricing, features
     - Categories and tags

2. **Taxonomy Setup**:
   - Configure **AI Categories**, **AI Features**, and **Pricing Models** taxonomies
   - These power the filtering system on your directory pages

---

## Theme Styling Guide

### Color Scheme

The theme's cyberpunk neon styling uses these key colors:

- **Background Colors**:
  - Main Background: `#0a0a0a` (Almost Black)
  - Secondary Background: `#222222` (Dark Gray)

- **Neon Accent Colors**:
  - Neon Orange: `#FFA500` (Primary accent)
  - Neon Purple: `#BF00FF` (Secondary accent)
  - Neon Cyan: `#00FFFF` (Tertiary accent)

- **Text Colors**:
  - Main Text: `#cccccc` (Light Gray)
  - Muted Text: `#666666` (Medium Gray)

### Typography

- **Heading Font**: Orbitron (Sci-fi, futuristic font)
- **Body Font**: Roboto (Clean, legible sans-serif)

### CSS Classes

The theme includes several utility classes for creating neon effects:

- `.neon-title`: For glowing headings
- `.neon-link`: For glowing links
- `.neon-button`: For glowing buttons
- `.neon-icon-glow`: For glowing icons
- `.neon-pulse`: For elements with pulsing animation
- `.neon-border-pulse`: For elements with pulsing borders

---

## Using Theme Shortcodes

### Rating Display

```
[rating rating="4.5" max="5" class="neon-rating"]
```

- `rating`: Numeric rating value (required)
- `max`: Maximum rating scale (default: 5)
- `class`: Additional CSS classes

### Affiliate Links

```
[affiliate_link url="https://example.com" text="Try Now" class="neon-button"]
```

- `url`: Destination URL (required)
- `text`: Button/link text (default: "Try Now")
- `class`: CSS classes (default: "button")

### Social Sharing

```
[social_sharing]
```

Displays social sharing icons for the current post.

---

## JavaScript Features

### Particles Effect

The theme includes a custom particles effect for hero sections. To add it to any section:

1. Add the class `neon-hero` to your container
2. The effect will automatically initialize

### Filtering System

The AJAX filtering system allows users to filter content without page reloads:

- For blog posts: Use `.category-button` or `.neon-category-button` classes
- For AI tools: Use filter selects with `data-filter` attributes

---

## File Structure Overview

- `style.css`: Main theme styles including dark neon cyberpunk colors
- `functions.php`: Theme setup, custom post types, taxonomies, and shortcodes
- `assets/js/main.js`: JavaScript functionality including particles effect
- Template files:
  - `header.php` & `footer.php`: Site header and footer with neon styling
  - `front-page.php`: Homepage template with hero section
  - `single-ai_tool.php`: Single AI tool display template
  - `archive-ai_tool.php`: AI tools directory template with filters
  - `single.php` & `archive.php`: Blog post templates
  - `page.php`: Default page template

---

## Advanced Customization

### Adding Custom Color Schemes

1. Open `style.css`
2. Locate the `:root` CSS variables section
3. Modify the color values for your preferred scheme

### Adding New Post Types

1. Duplicate the AI tools post type registration in `functions.php`
2. Modify the labels and arguments for your new post type
3. Create corresponding single and archive templates

### Customizing Particle Effect

1. Open `assets/js/main.js`
2. Find the `heroParticles` function
3. Modify particle count, colors, size, and behavior

---

## Troubleshooting

### Common Issues

1. **Missing Icons**: Ensure Font Awesome is properly loaded
2. **Particle Effect Not Working**: Check for JavaScript errors in console
3. **Demo Content Images Missing**: Re-import with attachments checked
4. **Filter Not Working**: Verify AJAX URL is correct in the JavaScript

### Browser Compatibility

The theme is tested with:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

For older browsers, some neon effects may be less prominent.

---

## Support and Updates

For support requests, bug reports, or feature suggestions:

- Visit [https://inspiredshifter.com/support](https://inspiredshifter.com/support)
- Email: <EMAIL>

---

## Credits

- Font Awesome: [https://fontawesome.com](https://fontawesome.com)
- Google Fonts: [https://fonts.google.com](https://fonts.google.com)
- Particles.js: [https://vincentgarreau.com/particles.js](https://vincentgarreau.com/particles.js)

---

## License

This theme is released under the GPL v2 or later license.
[http://www.gnu.org/licenses/gpl-2.0.html](http://www.gnu.org/licenses/gpl-2.0.html)
