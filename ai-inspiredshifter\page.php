<?php
/**
 * The template for displaying all single pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package AI_InspiredShifter
 */

get_header();
?>

<main id="primary" class="site-main neon-theme">
    <div class="container neon-container">
        <div class="content-area neon-content-area">
            <?php
            while (have_posts()) :
                the_post();
            ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('page-content neon-page'); ?>>
                    <header class="page-header neon-page-header">
                        <?php the_title('<h1 class="page-title neon-title">', '</h1>'); ?>
                    </header><!-- .page-header -->

                    <?php if (has_post_thumbnail()) : ?>
                        <div class="featured-image neon-featured-image">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="entry-content neon-content">
                        <?php
                        the_content();

                        wp_link_pages(
                            array(
                                'before' => '<div class="page-links neon-page-links">' . esc_html__('Pages:', 'ai-inspiredshifter'),
                                'after'  => '</div>',
                            )
                        );
                        ?>
                    </div><!-- .entry-content -->
                </article><!-- #post-<?php the_ID(); ?> -->

                <?php
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
            endwhile; // End of the loop.
            ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main><!-- #main -->

<?php
get_footer();