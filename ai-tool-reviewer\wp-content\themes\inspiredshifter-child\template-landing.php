<?php
/**
 * Template Name: Email Capture Landing Page
 *
 * @package InspiredShifter Child
 */

get_header();
?>

<div class="landing-page">
    <div class="landing-hero">
        <div class="container">
            <div class="landing-content">
                <h1 class="landing-title"><?php the_title(); ?></h1>
                
                <?php if (get_field('landing_subtitle')) : ?>
                    <p class="landing-subtitle"><?php echo get_field('landing_subtitle'); ?></p>
                <?php endif; ?>
                
                <?php if (have_rows('landing_benefits')) : ?>
                    <div class="landing-benefits">
                        <ul class="benefits-list">
                            <?php while (have_rows('landing_benefits')) : the_row(); ?>
                                <li class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    <span class="benefit-text"><?php the_sub_field('benefit'); ?></span>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="landing-form">
                    <?php 
                    // Check if Contact Form 7 is active and a form ID is provided
                    if (function_exists('wpcf7_contact_form_tag_func') && get_field('form_shortcode')) {
                        echo do_shortcode(get_field('form_shortcode'));
                    } else {
                        // Fallback form HTML if Contact Form 7 is not available
                    ?>
                        <form class="signup-form" method="post">
                            <div class="form-group">
                                <label for="name"><?php _e('Name', 'inspiredshifter-child'); ?></label>
                                <input type="text" id="name" name="name" placeholder="<?php _e('Your Name (Optional)', 'inspiredshifter-child'); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="email"><?php _e('Email', 'inspiredshifter-child'); ?> <span class="required">*</span></label>
                                <input type="email" id="email" name="email" placeholder="<?php _e('Your Email Address', 'inspiredshifter-child'); ?>" required>
                            </div>
                            
                            <div class="form-submit">
                                <button type="submit" class="submit-button"><?php _e('Subscribe Now', 'inspiredshifter-child'); ?></button>
                            </div>
                            
                            <div class="form-privacy">
                                <p><?php _e('We respect your privacy. Unsubscribe at any time.', 'inspiredshifter-child'); ?></p>
                            </div>
                        </form>
                    <?php } ?>
                </div>
                
                <div class="landing-thank-you" style="display: none;">
                    <h2><?php _e('Thank You!', 'inspiredshifter-child'); ?></h2>
                    <p><?php _e('Your submission has been received. Check your email for confirmation.', 'inspiredshifter-child'); ?></p>
                </div>
                
                <?php if (get_field('privacy_text')) : ?>
                    <div class="landing-privacy">
                        <p><?php echo get_field('privacy_text'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if (has_post_thumbnail()) : ?>
                <div class="landing-image">
                    <?php the_post_thumbnail('large', array('class' => 'landing-featured-image')); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (get_field('show_testimonials') && have_rows('testimonials')) : ?>
        <div class="landing-testimonials">
            <div class="container">
                <h2 class="testimonials-title"><?php _e('What People Are Saying', 'inspiredshifter-child'); ?></h2>
                
                <div class="testimonials-grid">
                    <?php while (have_rows('testimonials')) : the_row(); 
                        $name = get_sub_field('name');
                        $position = get_sub_field('position');
                        $quote = get_sub_field('quote');
                        $image = get_sub_field('image');
                    ?>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="testimonial-quote"><?php echo $quote; ?></div>
                            </div>
                            
                            <div class="testimonial-author">
                                <?php if ($image) : ?>
                                    <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($name); ?>" class="author-image">
                                <?php endif; ?>
                                
                                <div class="author-info">
                                    <div class="author-name"><?php echo esc_html($name); ?></div>
                                    <?php if ($position) : ?>
                                        <div class="author-position"><?php echo esc_html($position); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if (get_field('show_faq') && have_rows('faq_items')) : ?>
        <div class="landing-faq">
            <div class="container">
                <h2 class="faq-title"><?php _e('Frequently Asked Questions', 'inspiredshifter-child'); ?></h2>
                
                <div class="faq-list">
                    <?php while (have_rows('faq_items')) : the_row(); 
                        $question = get_sub_field('question');
                        $answer = get_sub_field('answer');
                    ?>
                        <div class="faq-item">
                            <div class="faq-question"><?php echo esc_html($question); ?></div>
                            <div class="faq-answer"><?php echo $answer; ?></div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="landing-cta">
        <div class="container">
            <h2 class="cta-title"><?php _e('Ready to Get Started?', 'inspiredshifter-child'); ?></h2>
            <p class="cta-text"><?php _e('Join our community today and discover the best AI tools for your needs.', 'inspiredshifter-child'); ?></p>
            
            <a href="#landing-form" class="cta-button"><?php _e('Subscribe Now', 'inspiredshifter-child'); ?></a>
        </div>
    </div>
</div>

<script>
(function($) {
    // Smooth scroll to form
    $('.cta-button').on('click', function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: $('.landing-form').offset().top - 100
        }, 800);
    });
    
    // Form submission handling
    $('.signup-form').on('submit', function(e) {
        e.preventDefault();
        
        // Here you would normally handle the AJAX form submission
        // For demo purposes, we'll just show the thank you message
        $('.landing-form').fadeOut(300, function() {
            $('.landing-thank-you').fadeIn(300);
        });
    });
    
    // FAQ accordion functionality
    $('.faq-question').on('click', function() {
        $(this).toggleClass('active');
        $(this).next('.faq-answer').slideToggle(300);
    });
})(jQuery);
</script>

<?php get_footer(); ?>