<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package AI_InspiredShifter
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item ' . get_post_type()); ?>>
    <div class="search-result-inner">
        <?php if (has_post_thumbnail()) : ?>
            <div class="search-result-image">
                <a href="<?php the_permalink(); ?>">
                    <?php the_post_thumbnail('thumbnail'); ?>
                </a>
            </div>
        <?php endif; ?>

        <div class="search-result-content">
            <header class="entry-header">
                <div class="result-type">
                    <?php
                    $post_type = get_post_type();
                    $post_type_obj = get_post_type_object($post_type);
                    
                    if ($post_type === 'post') {
                        echo '<span class="result-badge post"><i class="fas fa-file-alt"></i> ' . esc_html__('Blog Post', 'ai-inspiredshifter') . '</span>';
                    } elseif ($post_type === 'ai_tool') {
                        echo '<span class="result-badge ai_tool"><i class="fas fa-robot"></i> ' . esc_html__('AI Tool', 'ai-inspiredshifter') . '</span>';
                    } elseif ($post_type === 'page') {
                        echo '<span class="result-badge page"><i class="fas fa-file"></i> ' . esc_html__('Page', 'ai-inspiredshifter') . '</span>';
                    } else {
                        echo '<span class="result-badge other"><i class="fas fa-file-alt"></i> ' . esc_html($post_type_obj->labels->singular_name) . '</span>';
                    }
                    ?>
                </div>

                <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>

                <div class="entry-meta">
                    <span class="posted-on">
                        <i class="far fa-calendar-alt"></i>
                        <?php echo get_the_date(); ?>
                    </span>
                    
                    <?php if ($post_type === 'post' || $post_type === 'ai_tool') : ?>
                        <span class="posted-by">
                            <i class="far fa-user"></i>
                            <?php the_author_posts_link(); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($post_type === 'ai_tool') : ?>
                        <?php
                        $rating = get_post_meta(get_the_ID(), 'tool_rating', true);
                        if ($rating) :
                        ?>
                            <span class="tool-rating">
                                <i class="fas fa-star"></i>
                                <?php echo number_format($rating, 1); ?>/5
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
                </div><!-- .entry-meta -->
            </header><!-- .entry-header -->

            <div class="entry-summary">
                <?php the_excerpt(); ?>
            </div><!-- .entry-summary -->

            <footer class="entry-footer">
                <a href="<?php the_permalink(); ?>" class="read-more">
                    <?php
                    if ($post_type === 'ai_tool') {
                        esc_html_e('View Tool', 'ai-inspiredshifter');
                    } else {
                        esc_html_e('Read More', 'ai-inspiredshifter');
                    }
                    ?>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </footer><!-- .entry-footer -->
        </div>
    </div>
</article><!-- #post-<?php the_ID(); ?> -->
